<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link active">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-analytics.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span class="nav-text">Analytics</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fas fa-cog"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Dashboard</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Welcome Section -->
                <div class="page-header">
                    <h2 class="page-title">Welcome back, <span id="adminName">Admin</span>!</h2>
                    <p class="page-subtitle">Here's what's happening with your store today.</p>
                </div>

                <!-- Dashboard Cards -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalUsers">0</h3>
                                <p>Total Users</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="usersTrend">+12%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalOrders">0</h3>
                                <p>Total Orders</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="ordersTrend">+8%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon revenue">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalRevenue">$0</h3>
                                <p>Total Revenue</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="revenueTrend">+15%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalProducts">0</h3>
                                <p>Total Products</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="productsTrend">+5%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">Recent Users</h3>
                        <div class="table-actions">
                            <a href="admin-users.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i> View All
                            </a>
                        </div>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Join Date</th>
                                <th>Last Login</th>
                            </tr>
                        </thead>
                        <tbody id="recentUsersTable">
                            <!-- Users will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- User Menu Dropdown Styles -->
    <style>
        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize dashboard
            initializeDashboard();
            
            // Setup event listeners
            setupEventListeners();
        });

        function initializeDashboard() {
            const currentUser = authManager.getCurrentUser();
            document.getElementById('adminName').textContent = currentUser.firstName;

            // Load dashboard stats
            loadDashboardStats();
            loadRecentUsers();
        }

        function loadDashboardStats() {
            const userStats = authManager.getUserStats();
            
            document.getElementById('totalUsers').textContent = userStats.total;
            document.getElementById('totalOrders').textContent = '156'; // Mock data
            document.getElementById('totalRevenue').textContent = '$12,450';
            document.getElementById('totalProducts').textContent = '89';
        }

        function loadRecentUsers() {
            const users = authManager.getAllUsers()
                .sort((a, b) => new Date(b.joinDate) - new Date(a.joinDate))
                .slice(0, 5);

            const tbody = document.getElementById('recentUsersTable');
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div class="profile-avatar" style="width: 32px; height: 32px; font-size: 0.875rem;">
                                ${getInitials(user.firstName, user.lastName)}
                            </div>
                            <span>${user.firstName} ${user.lastName}</span>
                        </div>
                    </td>
                    <td>${user.email}</td>
                    <td><span class="status-badge ${getStatusBadgeClass(user.status)}">${user.status}</span></td>
                    <td>${formatDate(user.joinDate)}</td>
                    <td>${user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}</td>
                </tr>
            `).join('');
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');
                
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                authManager.logout();
            });
        }
    </script>
</body>
</html>
