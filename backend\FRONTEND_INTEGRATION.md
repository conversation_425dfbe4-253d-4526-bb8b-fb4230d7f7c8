# Frontend Integration Guide

This guide explains how to integrate the existing VAITH frontend with the new backend API.

## 🔄 Migration Strategy

### Phase 1: Authentication System
Replace the current localStorage-based authentication with API calls.

#### Update `js/auth.js`
```javascript
class AuthManager {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.token = localStorage.getItem('auth_token');
        this.currentUser = null;
        this.init();
    }

    async init() {
        if (this.token) {
            try {
                await this.getCurrentUser();
            } catch (error) {
                this.logout();
            }
        }
    }

    async login(email, password) {
        const response = await fetch(`${this.baseURL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        this.token = data.data.token;
        this.currentUser = data.data.user;
        
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        
        return this.currentUser;
    }

    async register(userData) {
        const response = await fetch(`${this.baseURL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        this.token = data.data.token;
        this.currentUser = data.data.user;
        
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        
        return this.currentUser;
    }

    async getCurrentUser() {
        const response = await fetch(`${this.baseURL}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        this.currentUser = data.data.user;
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        
        return this.currentUser;
    }

    logout() {
        this.token = null;
        this.currentUser = null;
        localStorage.removeItem('auth_token');
        localStorage.removeItem('currentUser');
        window.location.href = 'login.html';
    }

    isLoggedIn() {
        return this.token !== null && this.currentUser !== null;
    }

    isAdmin() {
        return this.currentUser && ['admin', 'super_admin'].includes(this.currentUser.role);
    }

    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
}
```

### Phase 2: Product Management
Update product loading to use the API.

#### Update `js/main.js`
```javascript
class ProductManager {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
    }

    async getProducts(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseURL}/products?${queryString}`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data;
    }

    async getProduct(id) {
        const response = await fetch(`${this.baseURL}/products/${id}`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data.product;
    }

    async getFeaturedProducts() {
        const response = await fetch(`${this.baseURL}/products/featured/list`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data.products;
    }

    async getSaleProducts(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseURL}/products/sale/list?${queryString}`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data;
    }
}

// Initialize product manager
const productManager = new ProductManager();

// Update existing functions
async function initializeProductGrid() {
    try {
        const featuredProducts = await productManager.getFeaturedProducts();
        const featuredContainer = document.getElementById('featuredProducts');
        if (featuredContainer) {
            renderProducts(featuredProducts.slice(0, 4), featuredContainer);
        }

        const trendingContainer = document.getElementById('trendingProducts');
        if (trendingContainer) {
            renderProducts(featuredProducts.slice(2, 6), trendingContainer);
        }
    } catch (error) {
        console.error('Failed to load products:', error);
    }
}
```

### Phase 3: Shopping Cart Integration
Replace localStorage cart with API calls.

#### Update `js/cart.js`
```javascript
class CartManager {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
    }

    async getCart() {
        if (!authManager.isLoggedIn()) {
            return { items: [], total_items: 0, total_amount: 0 };
        }

        const response = await fetch(`${this.baseURL}/cart`, {
            headers: authManager.getAuthHeaders()
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data.cart;
    }

    async addToCart(productId, quantity = 1, options = {}) {
        if (!authManager.isLoggedIn()) {
            // Store in localStorage for guest users
            this.addToLocalCart(productId, quantity, options);
            return;
        }

        const response = await fetch(`${this.baseURL}/cart/items`, {
            method: 'POST',
            headers: authManager.getAuthHeaders(),
            body: JSON.stringify({
                product_id: productId,
                quantity,
                options
            })
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        await this.updateCartDisplay();
        return data.data.cart;
    }

    async updateCartItem(productId, quantity) {
        if (!authManager.isLoggedIn()) {
            this.updateLocalCartItem(productId, quantity);
            return;
        }

        const response = await fetch(`${this.baseURL}/cart/items/${productId}`, {
            method: 'PUT',
            headers: authManager.getAuthHeaders(),
            body: JSON.stringify({ quantity })
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        await this.updateCartDisplay();
        return data.data.cart;
    }

    async removeFromCart(productId) {
        if (!authManager.isLoggedIn()) {
            this.removeFromLocalCart(productId);
            return;
        }

        const response = await fetch(`${this.baseURL}/cart/items/${productId}`, {
            method: 'DELETE',
            headers: authManager.getAuthHeaders()
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        await this.updateCartDisplay();
        return data.data.cart;
    }

    async syncCart() {
        if (!authManager.isLoggedIn()) return;

        const localCart = JSON.parse(localStorage.getItem('cart') || '[]');
        if (localCart.length === 0) return;

        try {
            const response = await fetch(`${this.baseURL}/cart/sync`, {
                method: 'POST',
                headers: authManager.getAuthHeaders(),
                body: JSON.stringify({ items: localCart })
            });

            const data = await response.json();
            
            if (data.success) {
                localStorage.removeItem('cart');
                await this.updateCartDisplay();
            }
        } catch (error) {
            console.error('Failed to sync cart:', error);
        }
    }

    async updateCartDisplay() {
        try {
            const cart = await this.getCart();
            this.renderCartItems(cart);
            this.updateCartCount(cart.total_items);
        } catch (error) {
            console.error('Failed to update cart display:', error);
        }
    }

    // Keep existing localStorage methods for guest users
    addToLocalCart(productId, quantity, options) {
        // Existing localStorage implementation
    }

    updateLocalCartItem(productId, quantity) {
        // Existing localStorage implementation
    }

    removeFromLocalCart(productId) {
        // Existing localStorage implementation
    }
}

// Initialize cart manager
const cartManager = new CartManager();

// Sync cart on login
authManager.onLogin = async () => {
    await cartManager.syncCart();
};
```

### Phase 4: Favorites Integration
Replace localStorage favorites with API calls.

#### Update `js/favorites.js`
```javascript
class FavoritesManager {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
    }

    async getFavorites() {
        if (!authManager.isLoggedIn()) {
            return JSON.parse(localStorage.getItem('favorites') || '[]');
        }

        const response = await fetch(`${this.baseURL}/favorites`, {
            headers: authManager.getAuthHeaders()
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }
        
        return data.data.favorites;
    }

    async toggleFavorite(productId) {
        if (!authManager.isLoggedIn()) {
            this.toggleLocalFavorite(productId);
            return;
        }

        const response = await fetch(`${this.baseURL}/favorites/${productId}`, {
            method: 'POST',
            headers: authManager.getAuthHeaders()
        });

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message);
        }

        await this.updateFavoritesDisplay();
        return data.data;
    }

    async isFavorite(productId) {
        if (!authManager.isLoggedIn()) {
            const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
            return favorites.includes(productId);
        }

        const response = await fetch(`${this.baseURL}/favorites/${productId}/status`, {
            headers: authManager.getAuthHeaders()
        });
        
        const data = await response.json();
        
        if (!data.success) {
            return false;
        }
        
        return data.data.is_favorite;
    }

    async updateFavoritesDisplay() {
        try {
            const favorites = await this.getFavorites();
            this.updateFavoritesCount(favorites.length);
            this.updateFavoriteButtons();
        } catch (error) {
            console.error('Failed to update favorites display:', error);
        }
    }

    // Keep existing localStorage methods for guest users
    toggleLocalFavorite(productId) {
        // Existing localStorage implementation
    }
}

// Initialize favorites manager
const favoritesManager = new FavoritesManager();
```

## 🔧 Configuration

### API Base URL Configuration
Create a config file for environment-specific settings:

```javascript
// js/config.js
const config = {
    development: {
        API_BASE_URL: 'http://localhost:3000/api'
    },
    production: {
        API_BASE_URL: 'https://api.vaith.com/api'
    }
};

const currentConfig = config[window.location.hostname === 'localhost' ? 'development' : 'production'];
```

### Error Handling
Implement global error handling:

```javascript
// js/api-client.js
class APIClient {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    async get(endpoint, headers = {}) {
        return this.request(endpoint, { method: 'GET', headers });
    }

    async post(endpoint, body, headers = {}) {
        return this.request(endpoint, {
            method: 'POST',
            headers,
            body: JSON.stringify(body)
        });
    }

    async put(endpoint, body, headers = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            headers,
            body: JSON.stringify(body)
        });
    }

    async delete(endpoint, headers = {}) {
        return this.request(endpoint, { method: 'DELETE', headers });
    }
}
```

## 🚀 Deployment Steps

1. **Backend Deployment**
   - Deploy backend to your server
   - Set up PostgreSQL database
   - Configure environment variables
   - Run migrations and seeding

2. **Frontend Updates**
   - Update API base URL in config
   - Test all functionality
   - Deploy updated frontend

3. **Testing**
   - Test user registration/login
   - Test product browsing and search
   - Test cart functionality
   - Test order placement
   - Test admin dashboard

## 📝 Migration Checklist

- [ ] Update authentication system
- [ ] Migrate product loading
- [ ] Integrate shopping cart
- [ ] Integrate favorites
- [ ] Update admin dashboard
- [ ] Test all user flows
- [ ] Update error handling
- [ ] Configure production URLs
- [ ] Deploy and test

This integration maintains the existing frontend design while adding robust backend functionality with persistent data storage and server-side processing.
