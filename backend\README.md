# VAITH E-commerce Backend API

A complete backend system for the VAITH fashion e-commerce website built with Node.js, Express, PostgreSQL, and Sequelize.

## 🚀 Features

### Core Functionality
- **User Authentication & Authorization** - JWT-based auth with role management
- **Product Management** - Full CRUD operations with categories, variants, and inventory
- **Shopping Cart** - Persistent cart with real-time updates
- **Order Processing** - Complete order lifecycle management
- **Favorites/Wishlist** - User favorite products management
- **Admin Dashboard** - Comprehensive admin operations and analytics

### Security Features
- Password hashing with bcrypt
- JWT token authentication
- Role-based access control
- Rate limiting
- Input validation and sanitization
- CORS protection
- Security headers with Helmet

### Database Features
- PostgreSQL with Sequelize ORM
- Proper relationships and constraints
- Database migrations and seeding
- Optimized queries with indexes
- Transaction support

## 📋 Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   # Database
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=vaith_ecommerce
   DB_USER=postgres
   DB_PASSWORD=your_password
   
   # JWT
   JWT_SECRET=your_super_secret_jwt_key
   JWT_EXPIRES_IN=7d
   
   # Server
   PORT=3000
   NODE_ENV=development
   ```

4. **Set up PostgreSQL database**
   ```sql
   CREATE DATABASE vaith_ecommerce;
   CREATE USER vaith_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE vaith_ecommerce TO vaith_user;
   ```

5. **Run migrations and seed data**
   ```bash
   npm run setup
   ```

6. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api
```

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <token>
```

### Product Endpoints

#### Get All Products
```http
GET /api/products?page=1&limit=12&category=women&search=dress&sort=price&order=ASC
```

#### Get Single Product
```http
GET /api/products/:id
```

#### Create Product (Admin)
```http
POST /api/products
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Product Name",
  "description": "Product description",
  "sku": "PRD-001",
  "price": 29.99,
  "category_id": 1,
  "stock_quantity": 100
}
```

### Cart Endpoints

#### Get Cart
```http
GET /api/cart
Authorization: Bearer <token>
```

#### Add Item to Cart
```http
POST /api/cart/items
Authorization: Bearer <token>
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 2,
  "options": {
    "size": "M",
    "color": "Blue"
  }
}
```

#### Update Cart Item
```http
PUT /api/cart/items/:productId
Authorization: Bearer <token>
Content-Type: application/json

{
  "quantity": 3
}
```

### Order Endpoints

#### Create Order
```http
POST /api/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "shipping_address_id": 1,
  "billing_address_id": 1,
  "payment_method": "credit_card"
}
```

#### Get User Orders
```http
GET /api/orders?page=1&limit=10&status=delivered
Authorization: Bearer <token>
```

### Admin Endpoints

#### Get Dashboard Stats
```http
GET /api/admin/dashboard
Authorization: Bearer <admin_token>
```

#### Manage Users
```http
GET /api/admin/users?page=1&search=john&status=active
Authorization: Bearer <admin_token>
```

#### Update Order Status
```http
PUT /api/admin/orders/:id/status
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "status": "shipped",
  "notes": "Shipped via FedEx"
}
```

## 🗄️ Database Schema

### Core Tables
- **users** - User accounts and profiles
- **categories** - Product categories (hierarchical)
- **products** - Product catalog with variants
- **carts** - User shopping carts
- **cart_items** - Items in shopping carts
- **orders** - Order records
- **order_items** - Items in orders
- **addresses** - User addresses
- **favorites** - User favorite products
- **reviews** - Product reviews

### Key Relationships
- Users have one Cart
- Users have many Orders, Addresses, Favorites, Reviews
- Categories can have parent Categories (hierarchical)
- Products belong to Categories
- Orders have many OrderItems
- Carts have many CartItems

## 🔧 Scripts

```bash
# Development
npm run dev          # Start with nodemon
npm run migrate      # Run database migrations
npm run seed         # Seed database with sample data
npm run setup        # Run migrations and seeding

# Production
npm start            # Start production server

# Testing
npm test             # Run tests
```

## 🌐 Frontend Integration

The backend is designed to work seamlessly with the existing VAITH frontend. Key integration points:

### Authentication
- Replace localStorage auth with API calls
- Use JWT tokens for authenticated requests
- Implement token refresh mechanism

### Product Display
- Fetch products from `/api/products` endpoint
- Support filtering, sorting, and pagination
- Handle product variants and stock status

### Shopping Cart
- Sync cart with backend on user login
- Real-time cart updates via API
- Persistent cart across sessions

### User Management
- Profile management through API
- Order history and tracking
- Address management

## 🔒 Security Considerations

- All passwords are hashed with bcrypt
- JWT tokens have expiration times
- Rate limiting prevents abuse
- Input validation on all endpoints
- CORS configured for frontend domains
- SQL injection prevention with Sequelize
- XSS protection with proper sanitization

## 📊 Monitoring & Logging

- Comprehensive logging with custom logger
- Request/response logging
- Error tracking and reporting
- Performance monitoring
- Security event logging

## 🚀 Deployment

### Environment Setup
1. Set up PostgreSQL database
2. Configure environment variables
3. Run migrations
4. Start the server

### Production Considerations
- Use PM2 for process management
- Set up reverse proxy (nginx)
- Configure SSL certificates
- Set up database backups
- Monitor logs and performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
