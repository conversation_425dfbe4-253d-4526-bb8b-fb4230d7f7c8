const bcrypt = require('bcryptjs');
const { User } = require('./models');

async function fixPassword() {
  try {
    console.log('🔧 Fixing demo user password...');
    
    // Hash the password correctly
    const hashedPassword = await bcrypt.hash('demo123', 10);
    console.log('🔐 New hash:', hashedPassword);
    
    // Update the user
    const [updatedRows] = await User.update(
      { password: hashedPassword },
      { where: { email: '<EMAIL>' } }
    );
    
    if (updatedRows > 0) {
      console.log('✅ Password updated successfully');
      
      // Verify the update
      const user = await User.findOne({ where: { email: '<EMAIL>' } });
      const isValid = await bcrypt.compare('demo123', user.password);
      console.log('✅ Password verification:', isValid);
    } else {
      console.log('❌ No user updated');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixPassword().then(() => process.exit(0));
