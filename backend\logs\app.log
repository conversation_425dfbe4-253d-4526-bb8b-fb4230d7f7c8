[2025-06-14T11:40:36.752Z] INFO: Starting database migrations...
[2025-06-14T11:40:36.759Z] INFO: Database connection established successfully.
[2025-06-14T11:40:36.943Z] INFO: Database models synchronized successfully.
[2025-06-14T11:40:36.944Z] INFO: Database migrations completed successfully!
[2025-06-14T11:40:36.944Z] INFO: Migrations completed
[2025-06-14T11:40:37.621Z] INFO: Starting database seeding...
[2025-06-14T11:40:37.644Z] INFO: Created 8 categories
[2025-06-14T11:40:37.649Z] INFO: Created 3 users
[2025-06-14T11:40:37.655Z] INFO: Created 6 products
[2025-06-14T11:40:37.663Z] INFO: Created 2 addresses
[2025-06-14T11:40:37.670Z] INFO: Created 2 orders
[2025-06-14T11:40:37.675Z] INFO: Created 3 order items
[2025-06-14T11:40:37.675Z] INFO: Database seeding completed successfully!
[2025-06-14T11:40:37.676Z] INFO: Seeding completed
[2025-06-14T11:44:01.269Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:44:01.273Z] INFO: Database connection established successfully.
[2025-06-14T11:44:01.275Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='users';
[2025-06-14T11:44:01.278Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.279Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.281Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.282Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.285Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.287Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.289Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.290Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.291Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.293Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.303Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.309Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.317Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.325Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.331Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.338Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.340Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.341Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.341Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.343Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.349Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.356Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.369Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.375Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.382Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.389Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.391Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.392Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.392Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.393Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.400Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.406Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.411Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.418Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.423Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.428Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.429Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.430Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.432Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.436Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.441Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.445Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.452Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.457Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.462Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.470Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.472Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.473Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.474Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.475Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.479Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.489Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.493Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.500Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.505Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.516Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.519Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.520Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.522Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.523Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.527Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.538Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.543Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.547Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.554Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.558Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.559Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.560Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.560Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.561Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.568Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.583Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.614Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.652Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.673Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.679Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.687Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.689Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.691Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.692Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.701Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.708Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.721Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.726Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.738Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.752Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.758Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.759Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.761Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.762Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.773Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.777Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.787Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.794Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.799Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.806Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.807Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.807Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.808Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.810Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.816Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.822Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.868Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.890Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.894Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.902Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.903Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.904Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.905Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.906Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.910Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.918Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.923Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.927Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.932Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.938Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.939Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.940Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.941Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.942Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.946Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.954Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.958Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.963Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:01.969Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:01.973Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:01.974Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:01.975Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:01.976Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:01.977Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:01.985Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:01.990Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:01.995Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.004Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:02.016Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:02.024Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:02.026Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:02.027Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:02.028Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:02.030Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.037Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:02.042Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:02.049Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.056Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:02.060Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:02.065Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:02.067Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:02.069Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:02.070Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:02.071Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.075Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:02.079Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:02.086Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.090Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:02.094Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:02.099Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:02.101Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:02.103Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:02.104Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:02.105Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.109Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:02.114Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:02.121Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.125Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:02.129Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:02.136Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:02.137Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:02.138Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:02.139Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:02.139Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.144Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:02.154Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:02.158Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.164Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:02.170Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:02.174Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:02.175Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:02.176Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='categories';
[2025-06-14T11:44:02.177Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:02.178Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:02.179Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:02.180Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:02.182Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:02.183Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:02.185Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:02.185Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:02.186Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:02.187Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:02.188Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:02.189Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:02.190Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:02.190Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:02.191Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:02.191Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:02.192Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:02.192Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:02.193Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:02.195Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `categories_backup` (`id` INTEGER PRIMARY KEY, `name` VARCHAR(255) NOT NULL, `slug` VARCHAR(255) NOT NULL, `description` TEXT, `parent_id` INTEGER REFERENCES `categories` (`id`), `image` VARCHAR(255), `icon` VARCHAR(255), `sort_order` INTEGER DEFAULT '0', `status` TEXT DEFAULT 'active', `featured` TINYINT(1) DEFAULT 0, `meta_title` VARCHAR(255), `meta_description` TEXT, `product_count` INTEGER DEFAULT '0', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:02.201Z] DEBUG: Executing (default): INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;
[2025-06-14T11:44:02.206Z] DEBUG: Executing (default): DROP TABLE `categories`;
[2025-06-14T11:44:02.209Z] ERROR: Unable to start server: {"name":"SequelizeForeignKeyConstraintError","parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"DROP TABLE `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"DROP TABLE `categories`;"},"sql":"DROP TABLE `categories`;","parameters":{}}
[2025-06-14T11:44:21.634Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:44:21.637Z] INFO: Database connection established successfully.
[2025-06-14T11:44:21.641Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='users';
[2025-06-14T11:44:21.644Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.644Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.646Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.647Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.648Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.649Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.650Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.652Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.653Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.655Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.668Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.673Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.680Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.685Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.693Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.698Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.699Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.700Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.701Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.702Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.709Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.715Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.720Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.727Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.732Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.736Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.737Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.740Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.741Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.743Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.748Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.752Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.757Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.763Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.767Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.771Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.774Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.776Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.777Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.778Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.783Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.787Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.794Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.799Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.803Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.810Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.811Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.812Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.813Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.814Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.818Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.823Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.830Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.834Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.840Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.845Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.846Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.847Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.847Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.848Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.886Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.912Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.921Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.929Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.934Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.937Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.940Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.942Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.943Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.945Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.950Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.954Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.960Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.965Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:21.969Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:21.975Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:21.976Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:21.977Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:21.978Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:21.979Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.983Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:21.988Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:21.994Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:21.999Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.003Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.009Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.010Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.012Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.013Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.014Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.018Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.024Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.029Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.034Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.040Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.047Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.049Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.049Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.050Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.052Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.059Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.065Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.069Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.075Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.080Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.084Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.085Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.085Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.086Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.087Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.093Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.097Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.101Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.106Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.112Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.116Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.117Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.118Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.118Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.119Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.125Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.130Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.134Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.138Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.145Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.149Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.150Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.151Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.152Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.153Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.164Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.180Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.184Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.190Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.196Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.200Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.201Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.202Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.202Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.203Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.210Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.215Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.219Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.227Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.231Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.235Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.236Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.237Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.240Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.243Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.248Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.254Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.260Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.267Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.271Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.278Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.279Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.279Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.280Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.281Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.285Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.290Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.295Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.299Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.303Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.309Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:22.311Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.313Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.313Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:22.314Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.318Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:22.323Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:22.329Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.333Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:22.337Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:22.342Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:22.343Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:22.345Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='categories';
[2025-06-14T11:44:22.346Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:22.346Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:22.347Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:22.348Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:22.348Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:22.348Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:22.349Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:22.349Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:22.349Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:22.351Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:22.351Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:22.352Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:22.353Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:22.353Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:22.353Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:22.354Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:22.354Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:22.354Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:22.357Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:22.360Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `categories_backup` (`id` INTEGER PRIMARY KEY, `name` VARCHAR(255) NOT NULL, `slug` VARCHAR(255) NOT NULL, `description` TEXT, `parent_id` INTEGER REFERENCES `categories` (`id`), `image` VARCHAR(255), `icon` VARCHAR(255), `sort_order` INTEGER DEFAULT '0', `status` TEXT DEFAULT 'active', `featured` TINYINT(1) DEFAULT 0, `meta_title` VARCHAR(255), `meta_description` TEXT, `product_count` INTEGER DEFAULT '0', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:22.376Z] DEBUG: Executing (default): INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;
[2025-06-14T11:44:22.378Z] ERROR: Unable to start server: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T11:44:45.307Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:44:45.310Z] INFO: Database connection established successfully.
[2025-06-14T11:44:45.313Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='users';
[2025-06-14T11:44:45.315Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.315Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.316Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.318Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.319Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.320Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.322Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.323Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.324Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.326Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.340Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.345Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.351Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.359Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.364Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.372Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.374Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.375Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.376Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.378Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.385Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.393Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.399Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.411Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.415Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.424Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.426Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.426Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.427Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.429Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.436Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.444Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.448Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.457Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.498Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.513Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.515Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.516Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.517Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.520Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.525Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.530Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.536Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.543Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.546Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.551Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.554Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.555Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.556Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.557Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.561Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.565Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.572Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.577Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.581Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.587Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.589Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.589Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.590Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.591Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.595Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.599Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.606Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.610Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.614Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.652Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.656Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.657Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.659Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.661Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.692Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.699Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.706Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.710Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.715Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.721Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.723Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.724Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.725Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.726Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.731Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.737Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.742Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.746Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.751Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.758Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.759Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.759Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.760Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.761Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.766Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.773Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.777Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.781Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.788Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.792Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.793Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.794Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.795Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.795Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.799Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.807Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.811Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.816Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.822Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.826Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.827Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.828Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.829Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.830Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.835Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.842Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.846Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.852Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.858Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.862Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.863Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.863Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.864Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.865Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.871Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.876Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.880Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.886Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.892Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.896Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.897Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.898Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.899Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.900Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.908Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.912Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.917Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.924Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.928Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.933Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.937Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.938Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.940Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.941Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.945Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.948Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.955Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.960Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:45.964Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:45.970Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:45.972Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:45.972Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:45.973Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:45.974Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:45.990Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:45.994Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:45.998Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.004Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:46.008Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:46.013Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:46.014Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:46.014Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:46.015Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:46.015Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.022Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:46.026Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:46.030Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.035Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:46.041Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:46.045Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`users`);
[2025-06-14T11:44:46.046Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:46.047Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:46.047Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`users`)
[2025-06-14T11:44:46.048Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users_backup` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.054Z] DEBUG: Executing (default): INSERT INTO `users_backup` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users`;
[2025-06-14T11:44:46.058Z] DEBUG: Executing (default): DROP TABLE `users`;
[2025-06-14T11:44:46.062Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY, `email` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(255) NOT NULL, `last_name` VARCHAR(255) NOT NULL, `phone` VARCHAR(255), `avatar` VARCHAR(255), `role` TEXT DEFAULT 'customer', `status` TEXT DEFAULT 'active', `email_verified` TINYINT(1) DEFAULT 0, `email_verification_token` VARCHAR(255), `password_reset_token` VARCHAR(255), `password_reset_expires` DATETIME, `last_login` DATETIME, `preferences` JSONB DEFAULT '{"notifications":true,"newsletter":false,"dark_mode":false,"language":"en"}', `stats` JSONB DEFAULT '{"total_orders":0,"total_spent":0,"favorite_items":0}', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.067Z] DEBUG: Executing (default): INSERT INTO `users` SELECT `id`, `email`, `password`, `first_name`, `last_name`, `phone`, `avatar`, `role`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `preferences`, `stats`, `created_at`, `updated_at` FROM `users_backup`;
[2025-06-14T11:44:46.073Z] DEBUG: Executing (default): DROP TABLE `users_backup`;
[2025-06-14T11:44:46.077Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`users`)
[2025-06-14T11:44:46.078Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_users_1`)
[2025-06-14T11:44:46.079Z] DEBUG: Executing (default): SELECT name FROM sqlite_master WHERE type='table' AND name='categories';
[2025-06-14T11:44:46.080Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:46.080Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:46.081Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:46.081Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:46.082Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:46.082Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:46.082Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:46.083Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:46.085Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:46.088Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:46.089Z] DEBUG: Executing (default): PRAGMA TABLE_INFO(`categories`);
[2025-06-14T11:44:46.090Z] DEBUG: Executing (default): PRAGMA INDEX_LIST(`categories`)
[2025-06-14T11:44:46.091Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`sqlite_autoindex_categories_1`)
[2025-06-14T11:44:46.091Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_slug`)
[2025-06-14T11:44:46.091Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_parent_id`)
[2025-06-14T11:44:46.092Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_status`)
[2025-06-14T11:44:46.092Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_featured`)
[2025-06-14T11:44:46.092Z] DEBUG: Executing (default): PRAGMA INDEX_INFO(`categories_sort_order`)
[2025-06-14T11:44:46.094Z] DEBUG: Executing (default): PRAGMA foreign_key_list(`categories`)
[2025-06-14T11:44:46.094Z] DEBUG: Executing (default): CREATE TABLE IF NOT EXISTS `categories_backup` (`id` INTEGER PRIMARY KEY, `name` VARCHAR(255) NOT NULL, `slug` VARCHAR(255) NOT NULL, `description` TEXT, `parent_id` INTEGER REFERENCES `categories` (`id`), `image` VARCHAR(255), `icon` VARCHAR(255), `sort_order` INTEGER DEFAULT '0', `status` TEXT DEFAULT 'active', `featured` TINYINT(1) DEFAULT 0, `meta_title` VARCHAR(255), `meta_description` TEXT, `product_count` INTEGER DEFAULT '0', `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL);
[2025-06-14T11:44:46.095Z] DEBUG: Executing (default): INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;
[2025-06-14T11:44:46.097Z] ERROR: Unable to start server: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T11:45:22.081Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:45:22.085Z] INFO: Database connection established successfully.
[2025-06-14T11:45:22.085Z] INFO: Using existing database.
[2025-06-14T11:45:22.089Z] INFO: 🚀 VAITH Backend Server running on port 3000
[2025-06-14T11:45:22.090Z] INFO: 📊 Environment: development
[2025-06-14T11:45:22.091Z] INFO: 🔗 API Base URL: http://localhost:3000/api
[2025-06-14T11:45:54.071Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:45:54.075Z] INFO: Database connection established successfully.
[2025-06-14T11:45:54.075Z] INFO: Using existing database.
[2025-06-14T11:46:48.446Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:46:48.450Z] INFO: Database connection established successfully.
[2025-06-14T11:46:48.450Z] INFO: Using existing database.
[2025-06-14T11:46:48.453Z] INFO: 🚀 VAITH Backend Server running on port 3000
[2025-06-14T11:46:48.453Z] INFO: 📊 Environment: development
[2025-06-14T11:46:48.454Z] INFO: 🔗 API Base URL: http://localhost:3000/api
[2025-06-14T11:47:34.413Z] DEBUG: Executing (default): SELECT 1+1 AS result
[2025-06-14T11:47:34.417Z] INFO: Database connection established successfully.
[2025-06-14T11:47:34.418Z] INFO: Using existing database.
[2025-06-14T11:47:34.421Z] INFO: 🚀 VAITH Backend Server running on port 3001
[2025-06-14T11:47:34.422Z] INFO: 📊 Environment: development
[2025-06-14T11:47:34.422Z] INFO: 🔗 API Base URL: http://localhost:3001/api
[2025-06-14T11:47:47.849Z] INFO: ::1 - - [14/Jun/2025:11:47:47 +0000] "GET /api HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-06-14T11:47:47.959Z] INFO: ::1 - - [14/Jun/2025:11:47:47 +0000] "GET /favicon.ico HTTP/1.1" 404 45 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-06-14T12:56:15.742Z] INFO: Database connection established successfully.
[2025-06-14T12:56:15.743Z] INFO: Using existing database.
[2025-06-14T12:56:15.747Z] INFO: 🚀 VAITH Backend Server running on port 3000
[2025-06-14T12:56:15.747Z] INFO: 📊 Environment: undefined
[2025-06-14T12:56:15.747Z] INFO: 🔗 API Base URL: http://localhost:3000/api
[2025-06-14T12:59:54.869Z] INFO: ::1 - - [14/Jun/2025:12:59:54 +0000] "GET /health HTTP/1.1" 200 75 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
[2025-06-14T13:02:14.071Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:02:14.073Z] INFO: ::1 - - [14/Jun/2025:13:02:14 +0000] "GET /api/products HTTP/1.1" 500 67 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
[2025-06-14T13:02:44.118Z] INFO: Starting database migrations...
[2025-06-14T13:02:44.125Z] INFO: Database connection established successfully.
[2025-06-14T13:02:45.135Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:02:45.135Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:03:29.280Z] INFO: Starting database seeding...
[2025-06-14T13:03:29.294Z] ERROR: Database seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"}
[2025-06-14T13:03:29.295Z] ERROR: Seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"}
[2025-06-14T13:03:53.908Z] INFO: Starting database migrations...
[2025-06-14T13:03:53.915Z] INFO: Database connection established successfully.
[2025-06-14T13:03:54.899Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:03:54.899Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:07:55.100Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:07:55.102Z] INFO: ::1 - - [14/Jun/2025:13:07:55 +0000] "GET /api/products HTTP/1.1" 500 67 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
[2025-06-14T13:09:35.263Z] INFO: Starting database migrations...
[2025-06-14T13:09:35.269Z] INFO: Database connection established successfully.
[2025-06-14T13:09:36.260Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:09:36.260Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:00.063Z] INFO: Starting database migrations...
[2025-06-14T13:11:00.069Z] INFO: Database connection established successfully.
[2025-06-14T13:11:01.093Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:01.094Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:15.665Z] INFO: Starting database seeding...
[2025-06-14T13:11:15.680Z] ERROR: Database seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"}
[2025-06-14T13:11:15.681Z] ERROR: Seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"}
[2025-06-14T13:14:08.044Z] INFO: Database connection established successfully.
[2025-06-14T13:14:08.045Z] INFO: Using existing database.
[2025-06-14T13:14:08.048Z] INFO: 🚀 VAITH Backend Server running on port 3000
[2025-06-14T13:14:08.049Z] INFO: 📊 Environment: undefined
[2025-06-14T13:14:08.049Z] INFO: 🔗 API Base URL: http://localhost:3000/api
[2025-06-14T13:15:25.076Z] INFO: ::1 - - [14/Jun/2025:13:15:25 +0000] "GET /health HTTP/1.1" 200 74 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
[2025-06-14T13:15:48.727Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:15:48.729Z] INFO: ::1 - - [14/Jun/2025:13:15:48 +0000] "GET /api/products HTTP/1.1" 500 67 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
[2025-06-14T13:17:18.816Z] INFO: Starting database migrations...
[2025-06-14T13:17:18.823Z] INFO: Database connection established successfully.
[2025-06-14T13:17:19.300Z] INFO: Database models synchronized successfully.
[2025-06-14T13:17:19.300Z] INFO: Database migrations completed successfully!
[2025-06-14T13:17:19.300Z] INFO: Migrations completed
[2025-06-14T13:22:06.516Z] INFO: Database connection established successfully.
[2025-06-14T13:22:06.517Z] INFO: Using existing database.
[2025-06-14T13:22:06.522Z] INFO: 🚀 VAITH Backend Server running on port 3000
[2025-06-14T13:22:06.522Z] INFO: 📊 Environment: undefined
[2025-06-14T13:22:06.522Z] INFO: 🔗 API Base URL: http://localhost:3000/api
[2025-06-14T13:22:46.691Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:22:46.697Z] INFO: ::1 - - [14/Jun/2025:13:22:46 +0000] "GET /api/products HTTP/1.1" 500 67 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.5965"
