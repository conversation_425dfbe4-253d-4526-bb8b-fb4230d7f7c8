[2025-06-14T11:44:02.209Z] ERROR: Unable to start server: {"name":"SequelizeForeignKeyConstraintError","parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"DROP TABLE `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"DROP TABLE `categories`;"},"sql":"DROP TABLE `categories`;","parameters":{}}
[2025-06-14T11:44:22.378Z] ERROR: Unable to start server: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T11:44:46.097Z] ERROR: Unable to start server: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:02:14.071Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:02:45.135Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:02:45.135Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:03:29.294Z] ERROR: Database seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"}
[2025-06-14T13:03:29.295Z] ERROR: Seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:03:29.281 +00:00','2025-06-14 13:03:29.281 +00:00',NULL);"}
[2025-06-14T13:03:54.899Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:03:54.899Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:07:55.100Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:09:36.260Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:09:36.260Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:01.093Z] ERROR: Database migration failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:01.094Z] ERROR: Migrations failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"id must be unique","type":"unique violation","path":"id","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"},"fields":["id"],"sql":"INSERT INTO `categories_backup` SELECT `id`, `name`, `slug`, `description`, `parent_id`, `image`, `icon`, `sort_order`, `status`, `featured`, `meta_title`, `meta_description`, `product_count`, `created_at`, `updated_at` FROM `categories`;"}
[2025-06-14T13:11:15.680Z] ERROR: Database seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"}
[2025-06-14T13:11:15.681Z] ERROR: Seeding failed: {"name":"SequelizeUniqueConstraintError","errors":[{"message":"slug must be unique","type":"unique violation","path":"slug","value":null,"origin":"DB","instance":null,"validatorKey":"not_unique","validatorName":null,"validatorArgs":[]}],"parent":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"original":{"errno":19,"code":"SQLITE_CONSTRAINT","sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"},"fields":["slug"],"sql":"INSERT INTO `categories` (`id`,`name`,`slug`,`description`,`sort_order`,`status`,`featured`,`product_count`,`created_at`,`updated_at`,`parent_id`) VALUES (NULL,'Women','women','Fashion for women',1,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Men','men','Fashion for men',2,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Dresses','dresses','Women''s dresses',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Tops','tops','Women''s tops and blouses',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',1),(NULL,'Shirts','shirts','Men''s shirts',1,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Pants','pants','Men''s pants and trousers',2,'active',0,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',2),(NULL,'Accessories','accessories','Fashion accessories',3,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL),(NULL,'Shoes','shoes','Footwear collection',4,'active',1,0,'2025-06-14 13:11:15.667 +00:00','2025-06-14 13:11:15.667 +00:00',NULL);"}
[2025-06-14T13:15:48.727Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
[2025-06-14T13:22:46.691Z] ERROR: SQLITE_ERROR: no such table: products {"stack":"Error\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:185:27)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:50\n    at new Promise (<anonymous>)\n    at Query.run (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\query.js:183:12)\n    at C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SQLiteQueryInterface.rawSelect (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Product.aggregate (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Product.count (C:\\Users\\<USER>\\Desktop\\‏‏‏‏FINALRESULT  - with backend\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)","name":"SequelizeDatabaseError"}
