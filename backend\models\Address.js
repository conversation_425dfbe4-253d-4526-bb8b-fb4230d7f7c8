module.exports = (sequelize, DataTypes) => {
  const Address = sequelize.define('Address', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.ENUM('shipping', 'billing', 'both'),
      defaultValue: 'shipping'
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    company: {
      type: DataTypes.STRING,
      allowNull: true
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: false
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true
    },
    city: {
      type: DataTypes.STRING,
      allowNull: false
    },
    state: {
      type: DataTypes.STRING,
      allowNull: false
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: false
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'US'
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    tableName: 'addresses',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['is_default']
      }
    ]
  });

  // Instance methods
  Address.prototype.getFullName = function() {
    return `${this.first_name} ${this.last_name}`;
  };

  Address.prototype.getFormattedAddress = function() {
    let address = `${this.address_line_1}`;
    if (this.address_line_2) {
      address += `, ${this.address_line_2}`;
    }
    address += `\n${this.city}, ${this.state} ${this.postal_code}`;
    address += `\n${this.country}`;
    return address;
  };

  Address.prototype.setAsDefault = async function() {
    // Remove default flag from other addresses of the same type
    await Address.update(
      { is_default: false },
      {
        where: {
          user_id: this.user_id,
          type: this.type,
          id: { [sequelize.Op.ne]: this.id }
        }
      }
    );

    this.is_default = true;
    return this.save();
  };

  // Class methods
  Address.findByUser = function(userId, type = null) {
    const where = { user_id: userId };
    if (type) {
      where.type = type;
    }

    return this.findAll({
      where,
      order: [['is_default', 'DESC'], ['created_at', 'DESC']]
    });
  };

  Address.findDefaultByUser = function(userId, type = 'shipping') {
    return this.findOne({
      where: {
        user_id: userId,
        type,
        is_default: true
      }
    });
  };

  return Address;
};
