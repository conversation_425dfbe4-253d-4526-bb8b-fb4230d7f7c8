module.exports = (sequelize, DataTypes) => {
  const Cart = sequelize.define('Cart', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    session_id: {
      type: DataTypes.STRING,
      allowNull: true
    },
    total_items: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'carts',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['session_id']
      }
    ]
  });

  // Instance methods
  Cart.prototype.calculateTotals = async function() {
    const { CartItem } = require('./index');
    
    const items = await CartItem.findAll({
      where: { cart_id: this.id },
      include: [{
        model: sequelize.models.Product,
        as: 'product'
      }]
    });

    let totalItems = 0;
    let totalAmount = 0;

    items.forEach(item => {
      totalItems += item.quantity;
      totalAmount += parseFloat(item.product.price) * item.quantity;
    });

    this.total_items = totalItems;
    this.total_amount = totalAmount.toFixed(2);
    
    return this.save();
  };

  Cart.prototype.isEmpty = function() {
    return this.total_items === 0;
  };

  Cart.prototype.clear = async function() {
    const { CartItem } = require('./index');
    
    await CartItem.destroy({
      where: { cart_id: this.id }
    });

    this.total_items = 0;
    this.total_amount = 0;
    
    return this.save();
  };

  Cart.prototype.addItem = async function(productId, quantity = 1, options = {}) {
    const { CartItem, Product } = require('./index');
    
    const product = await Product.findByPk(productId);
    if (!product) {
      throw new Error('Product not found');
    }

    if (!product.isInStock()) {
      throw new Error('Product is out of stock');
    }

    if (quantity > product.stock_quantity) {
      throw new Error('Insufficient stock');
    }

    const existingItem = await CartItem.findOne({
      where: {
        cart_id: this.id,
        product_id: productId
      }
    });

    if (existingItem) {
      const newQuantity = existingItem.quantity + quantity;
      if (newQuantity > product.stock_quantity) {
        throw new Error('Insufficient stock');
      }
      
      existingItem.quantity = newQuantity;
      existingItem.options = { ...existingItem.options, ...options };
      await existingItem.save();
      
      await this.calculateTotals();
      return existingItem;
    } else {
      const cartItem = await CartItem.create({
        cart_id: this.id,
        product_id: productId,
        quantity,
        price: product.price,
        options
      });
      
      await this.calculateTotals();
      return cartItem;
    }
  };

  Cart.prototype.removeItem = async function(productId) {
    const { CartItem } = require('./index');
    
    const deleted = await CartItem.destroy({
      where: {
        cart_id: this.id,
        product_id: productId
      }
    });

    if (deleted) {
      await this.calculateTotals();
    }

    return deleted > 0;
  };

  Cart.prototype.updateItemQuantity = async function(productId, quantity) {
    const { CartItem, Product } = require('./index');
    
    if (quantity <= 0) {
      return this.removeItem(productId);
    }

    const product = await Product.findByPk(productId);
    if (!product) {
      throw new Error('Product not found');
    }

    if (quantity > product.stock_quantity) {
      throw new Error('Insufficient stock');
    }

    const cartItem = await CartItem.findOne({
      where: {
        cart_id: this.id,
        product_id: productId
      }
    });

    if (!cartItem) {
      throw new Error('Item not found in cart');
    }

    cartItem.quantity = quantity;
    await cartItem.save();
    
    await this.calculateTotals();
    return cartItem;
  };

  // Class methods
  Cart.findByUser = function(userId) {
    return this.findOne({ where: { user_id: userId } });
  };

  Cart.findOrCreateByUser = async function(userId) {
    const [cart, created] = await this.findOrCreate({
      where: { user_id: userId },
      defaults: { user_id: userId }
    });
    return cart;
  };

  Cart.findBySession = function(sessionId) {
    return this.findOne({ where: { session_id: sessionId } });
  };

  Cart.cleanupExpired = async function() {
    const { Op } = require('sequelize');
    
    return this.destroy({
      where: {
        expires_at: {
          [Op.lt]: new Date()
        }
      }
    });
  };

  return Cart;
};
