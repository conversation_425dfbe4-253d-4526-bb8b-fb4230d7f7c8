module.exports = (sequelize, DataTypes) => {
  const CartItem = sequelize.define('CartItem', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    cart_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'carts',
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    options: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Product options like size, color, etc.'
    }
  }, {
    tableName: 'cart_items',
    indexes: [
      {
        fields: ['cart_id']
      },
      {
        fields: ['product_id']
      },
      {
        unique: true,
        fields: ['cart_id', 'product_id']
      }
    ]
  });

  // Instance methods
  CartItem.prototype.getSubtotal = function() {
    return parseFloat(this.price) * this.quantity;
  };

  CartItem.prototype.updatePrice = async function() {
    const { Product } = require('./index');
    const product = await Product.findByPk(this.product_id);
    
    if (product) {
      this.price = product.price;
      return this.save();
    }
    
    throw new Error('Product not found');
  };

  return CartItem;
};
