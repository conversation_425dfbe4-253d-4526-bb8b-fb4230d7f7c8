module.exports = (sequelize, DataTypes) => {
  const Category = sequelize.define('Category', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        is: /^[a-z0-9-]+$/
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'categories',
        key: 'id'
      }
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    icon: {
      type: DataTypes.STRING,
      allowNull: true
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active'
    },
    featured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    meta_title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    meta_description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    product_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    }
  }, {
    tableName: 'categories',
    indexes: [
      {
        fields: ['slug']
      },
      {
        fields: ['parent_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['featured']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  // Instance methods
  Category.prototype.isParent = function() {
    return this.parent_id === null;
  };

  Category.prototype.isChild = function() {
    return this.parent_id !== null;
  };

  Category.prototype.getFullPath = async function() {
    if (this.isParent()) {
      return this.name;
    }

    const parent = await Category.findByPk(this.parent_id);
    if (parent) {
      return `${parent.name} > ${this.name}`;
    }
    return this.name;
  };

  Category.prototype.updateProductCount = async function() {
    const { Product } = require('./index');
    const count = await Product.count({
      where: {
        category_id: this.id,
        status: 'active'
      }
    });
    
    this.product_count = count;
    return this.save();
  };

  // Class methods
  Category.findActive = function() {
    return this.findAll({ 
      where: { status: 'active' },
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });
  };

  Category.findParents = function() {
    return this.findAll({ 
      where: { 
        status: 'active',
        parent_id: null 
      },
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });
  };

  Category.findChildren = function(parentId) {
    return this.findAll({ 
      where: { 
        status: 'active',
        parent_id: parentId 
      },
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });
  };

  Category.findFeatured = function() {
    return this.findAll({ 
      where: { 
        status: 'active',
        featured: true 
      },
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });
  };

  Category.findBySlug = function(slug) {
    return this.findOne({ 
      where: { 
        slug,
        status: 'active' 
      } 
    });
  };

  Category.getHierarchy = async function() {
    const parents = await this.findParents();
    const hierarchy = [];

    for (const parent of parents) {
      const children = await this.findChildren(parent.id);
      hierarchy.push({
        ...parent.toJSON(),
        children: children.map(child => child.toJSON())
      });
    }

    return hierarchy;
  };

  return Category;
};
