module.exports = (sequelize, DataTypes) => {
  const Favorite = sequelize.define('Favorite', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      }
    }
  }, {
    tableName: 'favorites',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['product_id']
      },
      {
        unique: true,
        fields: ['user_id', 'product_id']
      }
    ]
  });

  // Class methods
  Favorite.findByUser = function(userId) {
    return this.findAll({
      where: { user_id: userId },
      include: [{
        model: sequelize.models.Product,
        as: 'product'
      }],
      order: [['created_at', 'DESC']]
    });
  };

  Favorite.toggle = async function(userId, productId) {
    const existing = await this.findOne({
      where: {
        user_id: userId,
        product_id: productId
      }
    });

    if (existing) {
      await existing.destroy();
      return { action: 'removed', favorite: null };
    } else {
      const favorite = await this.create({
        user_id: userId,
        product_id: productId
      });
      return { action: 'added', favorite };
    }
  };

  Favorite.isFavorite = async function(userId, productId) {
    const favorite = await this.findOne({
      where: {
        user_id: userId,
        product_id: productId
      }
    });
    return !!favorite;
  };

  return Favorite;
};
