module.exports = (sequelize, DataTypes) => {
  const Order = sequelize.define('Order', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    order_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM(
        'pending',
        'confirmed',
        'processing',
        'shipped',
        'delivered',
        'cancelled',
        'refunded'
      ),
      defaultValue: 'pending'
    },
    payment_status: {
      type: DataTypes.ENUM(
        'pending',
        'paid',
        'failed',
        'refunded',
        'partially_refunded'
      ),
      defaultValue: 'pending'
    },
    payment_method: {
      type: DataTypes.STRING,
      allowNull: true
    },
    payment_id: {
      type: DataTypes.STRING,
      allowNull: true
    },
    subtotal: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    tax_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    shipping_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD'
    },
    shipping_address_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'addresses',
        key: 'id'
      }
    },
    billing_address_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'addresses',
        key: 'id'
      }
    },
    shipping_method: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tracking_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    admin_notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    shipped_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    delivered_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    cancelled_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    cancellation_reason: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'orders',
    hooks: {
      beforeCreate: async (order) => {
        if (!order.order_number) {
          order.order_number = await Order.generateOrderNumber();
        }
      }
    },
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['payment_status']
      },
      {
        fields: ['order_number']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // Instance methods
  Order.prototype.canBeCancelled = function() {
    return ['pending', 'confirmed'].includes(this.status);
  };

  Order.prototype.canBeShipped = function() {
    return ['confirmed', 'processing'].includes(this.status);
  };

  Order.prototype.canBeDelivered = function() {
    return this.status === 'shipped';
  };

  Order.prototype.isCompleted = function() {
    return this.status === 'delivered';
  };

  Order.prototype.isCancelled = function() {
    return this.status === 'cancelled';
  };

  Order.prototype.updateStatus = async function(newStatus, notes = null) {
    const oldStatus = this.status;
    this.status = newStatus;

    // Set timestamps based on status
    const now = new Date();
    switch (newStatus) {
      case 'shipped':
        this.shipped_at = now;
        break;
      case 'delivered':
        this.delivered_at = now;
        break;
      case 'cancelled':
        this.cancelled_at = now;
        if (notes) {
          this.cancellation_reason = notes;
        }
        break;
    }

    if (notes && newStatus !== 'cancelled') {
      this.admin_notes = notes;
    }

    await this.save();

    // Trigger status change hooks/notifications here
    // await this.notifyStatusChange(oldStatus, newStatus);

    return this;
  };

  Order.prototype.calculateTotals = function() {
    this.total_amount = parseFloat(this.subtotal) + 
                      parseFloat(this.tax_amount) + 
                      parseFloat(this.shipping_amount) - 
                      parseFloat(this.discount_amount);
    return this;
  };

  // Class methods
  Order.generateOrderNumber = async function() {
    const prefix = 'ORD';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${timestamp}-${random}`;
  };

  Order.findByUser = function(userId) {
    return this.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']]
    });
  };

  Order.findByStatus = function(status) {
    return this.findAll({
      where: { status },
      order: [['created_at', 'DESC']]
    });
  };

  Order.findPending = function() {
    return this.findByStatus('pending');
  };

  Order.findProcessing = function() {
    return this.findAll({
      where: {
        status: ['confirmed', 'processing']
      },
      order: [['created_at', 'ASC']]
    });
  };

  Order.findByOrderNumber = function(orderNumber) {
    return this.findOne({ where: { order_number: orderNumber } });
  };

  Order.getRevenueStats = async function(startDate, endDate) {
    const { Op } = require('sequelize');
    
    const whereClause = {
      status: 'delivered',
      payment_status: 'paid'
    };

    if (startDate && endDate) {
      whereClause.created_at = {
        [Op.between]: [startDate, endDate]
      };
    }

    const result = await this.findAll({
      where: whereClause,
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'order_count'],
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_revenue'],
        [sequelize.fn('AVG', sequelize.col('total_amount')), 'average_order_value']
      ],
      raw: true
    });

    return result[0];
  };

  return Order;
};
