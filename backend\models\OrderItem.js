module.exports = (sequelize, DataTypes) => {
  const OrderItem = sequelize.define('OrderItem', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'orders',
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      }
    },
    product_name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Snapshot of product name at time of order'
    },
    product_sku: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Snapshot of product SKU at time of order'
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Price per unit at time of order'
    },
    total_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Total price for this line item'
    },
    options: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Product options like size, color, etc.'
    },
    product_snapshot: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Complete product data snapshot at time of order'
    }
  }, {
    tableName: 'order_items',
    hooks: {
      beforeCreate: (orderItem) => {
        orderItem.total_price = parseFloat(orderItem.unit_price) * orderItem.quantity;
      },
      beforeUpdate: (orderItem) => {
        if (orderItem.changed('unit_price') || orderItem.changed('quantity')) {
          orderItem.total_price = parseFloat(orderItem.unit_price) * orderItem.quantity;
        }
      }
    },
    indexes: [
      {
        fields: ['order_id']
      },
      {
        fields: ['product_id']
      }
    ]
  });

  // Instance methods
  OrderItem.prototype.getSubtotal = function() {
    return parseFloat(this.total_price);
  };

  OrderItem.prototype.recalculateTotal = function() {
    this.total_price = parseFloat(this.unit_price) * this.quantity;
    return this;
  };

  return OrderItem;
};
