module.exports = (sequelize, DataTypes) => {
  const Product = sequelize.define('Product', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    short_description: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    sku: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    original_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'categories',
        key: 'id'
      }
    },
    brand: {
      type: DataTypes.STRING,
      allowNull: true
    },
    images: {
      type: DataTypes.JSONB,
      defaultValue: []
    },
    variants: {
      type: DataTypes.JSONB,
      defaultValue: {
        sizes: [],
        colors: [],
        materials: []
      }
    },
    stock_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    low_stock_threshold: {
      type: DataTypes.INTEGER,
      defaultValue: 10,
      validate: {
        min: 0
      }
    },
    weight: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    dimensions: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {
        length: null,
        width: null,
        height: null,
        unit: 'cm'
      }
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'draft', 'archived'),
      defaultValue: 'active'
    },
    featured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    on_sale: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    sale_start_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    sale_end_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    meta_title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    meta_description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: []
    },
    rating: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0,
      validate: {
        min: 0,
        max: 5
      }
    },
    review_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    view_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    sales_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    }
  }, {
    tableName: 'products',
    indexes: [
      {
        fields: ['category_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['featured']
      },
      {
        fields: ['on_sale']
      },
      {
        fields: ['price']
      },
      {
        fields: ['rating']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // Instance methods
  Product.prototype.isOnSale = function() {
    if (!this.on_sale) return false;
    
    const now = new Date();
    if (this.sale_start_date && now < this.sale_start_date) return false;
    if (this.sale_end_date && now > this.sale_end_date) return false;
    
    return true;
  };

  Product.prototype.getDiscountPercentage = function() {
    if (!this.original_price || !this.isOnSale()) return 0;
    return Math.round(((this.original_price - this.price) / this.original_price) * 100);
  };

  Product.prototype.isLowStock = function() {
    return this.stock_quantity <= this.low_stock_threshold;
  };

  Product.prototype.isInStock = function() {
    return this.stock_quantity > 0;
  };

  Product.prototype.incrementViewCount = function() {
    return this.increment('view_count');
  };

  Product.prototype.updateRating = async function() {
    const { Review } = require('./index');
    const reviews = await Review.findAll({
      where: { product_id: this.id },
      attributes: ['rating']
    });

    if (reviews.length === 0) {
      this.rating = 0;
      this.review_count = 0;
    } else {
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      this.rating = (totalRating / reviews.length).toFixed(2);
      this.review_count = reviews.length;
    }

    return this.save();
  };

  // Class methods
  Product.findActive = function() {
    return this.findAll({ where: { status: 'active' } });
  };

  Product.findFeatured = function() {
    return this.findAll({ 
      where: { 
        status: 'active',
        featured: true 
      } 
    });
  };

  Product.findOnSale = function() {
    return this.findAll({ 
      where: { 
        status: 'active',
        on_sale: true 
      } 
    });
  };

  Product.findByCategory = function(categoryId) {
    return this.findAll({ 
      where: { 
        status: 'active',
        category_id: categoryId 
      } 
    });
  };

  Product.findLowStock = function() {
    return this.findAll({
      where: sequelize.where(
        sequelize.col('stock_quantity'),
        '<=',
        sequelize.col('low_stock_threshold')
      )
    });
  };

  return Product;
};
