module.exports = (sequelize, DataTypes) => {
  const Review = sequelize.define('Review', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      }
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'orders',
        key: 'id'
      }
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    verified_purchase: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    helpful_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      defaultValue: 'pending'
    }
  }, {
    tableName: 'reviews',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['rating']
      },
      {
        fields: ['status']
      },
      {
        unique: true,
        fields: ['user_id', 'product_id']
      }
    ]
  });

  // Instance methods
  Review.prototype.approve = function() {
    this.status = 'approved';
    return this.save();
  };

  Review.prototype.reject = function() {
    this.status = 'rejected';
    return this.save();
  };

  // Class methods
  Review.findByProduct = function(productId, status = 'approved') {
    return this.findAll({
      where: { 
        product_id: productId,
        status 
      },
      include: [{
        model: sequelize.models.User,
        as: 'user',
        attributes: ['id', 'first_name', 'last_name', 'avatar']
      }],
      order: [['created_at', 'DESC']]
    });
  };

  Review.findByUser = function(userId) {
    return this.findAll({
      where: { user_id: userId },
      include: [{
        model: sequelize.models.Product,
        as: 'product',
        attributes: ['id', 'name', 'images']
      }],
      order: [['created_at', 'DESC']]
    });
  };

  Review.getAverageRating = async function(productId) {
    const result = await this.findAll({
      where: { 
        product_id: productId,
        status: 'approved'
      },
      attributes: [
        [sequelize.fn('AVG', sequelize.col('rating')), 'average'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      raw: true
    });

    return {
      average: parseFloat(result[0].average) || 0,
      count: parseInt(result[0].count) || 0
    };
  };

  return Review;
};
