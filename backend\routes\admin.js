const express = require('express');
const { Op } = require('sequelize');
const { User, Product, Order, OrderItem, Category } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { orderValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticateToken, requireAdmin);

// @desc    Get dashboard statistics
// @route   GET /api/admin/dashboard
// @access  Private/Admin
router.get('/dashboard', asyncHandler(async (req, res) => {
  const { period = '30' } = req.query; // days
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(period));

  // User statistics
  const totalUsers = await User.count();
  const newUsers = await User.count({
    where: {
      created_at: { [Op.gte]: startDate }
    }
  });
  const activeUsers = await User.count({
    where: { status: 'active' }
  });

  // Order statistics
  const totalOrders = await Order.count();
  const recentOrders = await Order.count({
    where: {
      created_at: { [Op.gte]: startDate }
    }
  });
  const pendingOrders = await Order.count({
    where: { status: 'pending' }
  });

  // Revenue statistics
  const revenueResult = await Order.findAll({
    where: {
      status: 'delivered',
      payment_status: 'paid'
    },
    attributes: [
      [require('sequelize').fn('SUM', require('sequelize').col('total_amount')), 'total_revenue'],
      [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'completed_orders']
    ],
    raw: true
  });

  const recentRevenueResult = await Order.findAll({
    where: {
      status: 'delivered',
      payment_status: 'paid',
      created_at: { [Op.gte]: startDate }
    },
    attributes: [
      [require('sequelize').fn('SUM', require('sequelize').col('total_amount')), 'recent_revenue']
    ],
    raw: true
  });

  // Product statistics
  const totalProducts = await Product.count();
  const activeProducts = await Product.count({
    where: { status: 'active' }
  });
  const lowStockProducts = await Product.count({
    where: {
      stock_quantity: { [Op.lte]: require('sequelize').col('low_stock_threshold') }
    }
  });

  // Top selling products
  const topProducts = await Product.findAll({
    attributes: [
      'id', 'name', 'sales_count', 'price', 'images'
    ],
    order: [['sales_count', 'DESC']],
    limit: 5
  });

  // Recent orders
  const recentOrdersList = await Order.findAll({
    include: [{
      model: User,
      as: 'user',
      attributes: ['id', 'first_name', 'last_name', 'email']
    }],
    order: [['created_at', 'DESC']],
    limit: 10
  });

  const stats = {
    users: {
      total: totalUsers,
      new: newUsers,
      active: activeUsers,
      growth: totalUsers > 0 ? ((newUsers / totalUsers) * 100).toFixed(1) : 0
    },
    orders: {
      total: totalOrders,
      recent: recentOrders,
      pending: pendingOrders,
      completed: parseInt(revenueResult[0]?.completed_orders) || 0
    },
    revenue: {
      total: parseFloat(revenueResult[0]?.total_revenue) || 0,
      recent: parseFloat(recentRevenueResult[0]?.recent_revenue) || 0,
      average_order: revenueResult[0]?.completed_orders > 0 ? 
        (parseFloat(revenueResult[0]?.total_revenue) / parseInt(revenueResult[0]?.completed_orders)).toFixed(2) : 0
    },
    products: {
      total: totalProducts,
      active: activeProducts,
      low_stock: lowStockProducts,
      inactive: totalProducts - activeProducts
    },
    top_products: topProducts,
    recent_orders: recentOrdersList
  };

  res.json({
    success: true,
    data: { stats }
  });
}));

// @desc    Get all users for admin management
// @route   GET /api/admin/users
// @access  Private/Admin
router.get('/users', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    search, 
    status, 
    role,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const where = {};

  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } }
    ];
  }

  if (status) {
    where.status = status;
  }

  if (role) {
    where.role = role;
  }

  const { count, rows: users } = await User.findAndCountAll({
    where,
    order: [[sort, order.toUpperCase()]],
    limit: parseInt(limit),
    offset,
    attributes: { exclude: ['password'] }
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Update user status
// @route   PUT /api/admin/users/:id/status
// @access  Private/Admin
router.put('/users/:id/status', commonValidations.id(), asyncHandler(async (req, res) => {
  const { status } = req.body;
  
  if (!['active', 'inactive', 'suspended'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid status'
    });
  }

  const user = await User.findByPk(req.params.id);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  await user.update({ status });

  res.json({
    success: true,
    message: `User status updated to ${status}`,
    data: { user }
  });
}));

// @desc    Get all orders for admin management
// @route   GET /api/admin/orders
// @access  Private/Admin
router.get('/orders', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    search, 
    status, 
    payment_status,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const where = {};

  if (search) {
    where[Op.or] = [
      { order_number: { [Op.iLike]: `%${search}%` } },
      { '$user.email$': { [Op.iLike]: `%${search}%` } },
      { '$user.first_name$': { [Op.iLike]: `%${search}%` } },
      { '$user.last_name$': { [Op.iLike]: `%${search}%` } }
    ];
  }

  if (status) {
    where.status = status;
  }

  if (payment_status) {
    where.payment_status = payment_status;
  }

  const { count, rows: orders } = await Order.findAndCountAll({
    where,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'first_name', 'last_name', 'email']
      },
      {
        model: OrderItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'images']
        }]
      }
    ],
    order: [[sort, order.toUpperCase()]],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Update order status
// @route   PUT /api/admin/orders/:id/status
// @access  Private/Admin
router.put('/orders/:id/status', commonValidations.id(), orderValidations.updateStatus, asyncHandler(async (req, res) => {
  const { status, notes } = req.body;

  const order = await Order.findByPk(req.params.id);
  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  await order.updateStatus(status, notes);

  res.json({
    success: true,
    message: `Order status updated to ${status}`,
    data: { order }
  });
}));

// @desc    Get products for admin management
// @route   GET /api/admin/products
// @access  Private/Admin
router.get('/products', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    search, 
    category_id, 
    status,
    low_stock = false,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const where = {};

  if (search) {
    where[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { sku: { [Op.iLike]: `%${search}%` } },
      { brand: { [Op.iLike]: `%${search}%` } }
    ];
  }

  if (category_id) {
    where.category_id = category_id;
  }

  if (status) {
    where.status = status;
  }

  if (low_stock === 'true') {
    where.stock_quantity = { [Op.lte]: require('sequelize').col('low_stock_threshold') };
  }

  const { count, rows: products } = await Product.findAndCountAll({
    where,
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name']
    }],
    order: [[sort, order.toUpperCase()]],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      products,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

module.exports = router;
