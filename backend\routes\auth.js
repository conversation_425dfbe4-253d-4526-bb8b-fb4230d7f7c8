const express = require('express');
const crypto = require('crypto');
const { User } = require('../models');
const { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken,
  authenticateToken 
} = require('../middleware/auth');
const { userValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
router.post('/register', userValidations.register, asyncHandler(async (req, res) => {
  const { email, password, first_name, last_name, phone } = req.body;

  // Check if user already exists
  const existingUser = await User.findByEmail(email);
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User already exists with this email'
    });
  }

  // Create user
  const user = await User.create({
    email,
    password,
    first_name,
    last_name,
    phone
  });

  // Generate tokens
  const token = generateToken(user);
  const refreshToken = generateRefreshToken(user);

  logger.info(`New user registered: ${email}`, { userId: user.id });

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user,
      token,
      refreshToken
    }
  });
}));

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
router.post('/login', userValidations.login, asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Find user and include password for validation
  const user = await User.findOne({
    where: { email },
    attributes: { include: ['password'] }
  });
  if (!user) {
    logger.logSecurityEvent('Failed login attempt', { email, reason: 'User not found' });
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }

  // Check password
  const isPasswordValid = await user.validatePassword(password);
  if (!isPasswordValid) {
    logger.logSecurityEvent('Failed login attempt', { email, reason: 'Invalid password' });
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password'
    });
  }

  // Check if account is active
  if (!user.isActive()) {
    logger.logSecurityEvent('Failed login attempt', { email, reason: 'Account inactive' });
    return res.status(401).json({
      success: false,
      message: 'Account is inactive. Please contact support.'
    });
  }

  // Update last login
  user.last_login = new Date();
  await user.save();

  // Generate tokens
  const token = generateToken(user);
  const refreshToken = generateRefreshToken(user);

  logger.info(`User logged in: ${email}`, { userId: user.id });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: user.toJSON(), // This excludes password
      token,
      refreshToken
    }
  });
}));

// @desc    Refresh access token
// @route   POST /api/auth/refresh
// @access  Public
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      success: false,
      message: 'Refresh token required'
    });
  }

  try {
    const decoded = verifyRefreshToken(refreshToken);
    const user = await User.findByPk(decoded.userId);

    if (!user || !user.isActive()) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Generate new access token
    const newToken = generateToken(user);

    res.json({
      success: true,
      data: {
        token: newToken
      }
    });
  } catch (error) {
    logger.logSecurityEvent('Invalid refresh token used', { error: error.message });
    return res.status(401).json({
      success: false,
      message: 'Invalid refresh token'
    });
  }
}));

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
router.get('/me', authenticateToken, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      user: req.user
    }
  });
}));

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
  // In a production app, you might want to blacklist the token
  // For now, we'll just return success
  logger.info(`User logged out: ${req.user.email}`, { userId: req.user.id });

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
}));

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
router.post('/forgot-password', asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  const user = await User.findByEmail(email);
  if (!user) {
    // Don't reveal if email exists or not
    return res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const resetTokenExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  user.password_reset_token = resetToken;
  user.password_reset_expires = resetTokenExpiry;
  await user.save();

  // TODO: Send email with reset link
  // await sendPasswordResetEmail(user.email, resetToken);

  logger.info(`Password reset requested for: ${email}`, { userId: user.id });

  res.json({
    success: true,
    message: 'If an account with that email exists, a password reset link has been sent.',
    // In development, return the token for testing
    ...(process.env.NODE_ENV === 'development' && { resetToken })
  });
}));

// @desc    Reset password
// @route   POST /api/auth/reset-password
// @access  Public
router.post('/reset-password', asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({
      success: false,
      message: 'Token and password are required'
    });
  }

  if (password.length < 6) {
    return res.status(400).json({
      success: false,
      message: 'Password must be at least 6 characters long'
    });
  }

  const user = await User.findOne({
    where: {
      password_reset_token: token,
      password_reset_expires: {
        [require('sequelize').Op.gt]: new Date()
      }
    }
  });

  if (!user) {
    return res.status(400).json({
      success: false,
      message: 'Invalid or expired reset token'
    });
  }

  // Update password and clear reset token
  user.password = password;
  user.password_reset_token = null;
  user.password_reset_expires = null;
  await user.save();

  logger.info(`Password reset completed for user: ${user.email}`, { userId: user.id });

  res.json({
    success: true,
    message: 'Password reset successful'
  });
}));

module.exports = router;
