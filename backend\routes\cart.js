const express = require('express');
const { Cart, CartItem, Product } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { cartValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Get user's cart
// @route   GET /api/cart
// @access  Private
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const cart = await Cart.findOne({
    where: { user_id: req.user.id },
    include: [{
      model: CartItem,
      as: 'items',
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'price', 'images', 'stock_quantity', 'status']
      }]
    }]
  });

  if (!cart) {
    return res.json({
      success: true,
      data: {
        cart: {
          items: [],
          total_items: 0,
          total_amount: 0
        }
      }
    });
  }

  // Filter out items with inactive products
  const activeItems = cart.items.filter(item => 
    item.product && item.product.status === 'active'
  );

  // If some items were filtered out, update the cart
  if (activeItems.length !== cart.items.length) {
    const inactiveItemIds = cart.items
      .filter(item => !item.product || item.product.status !== 'active')
      .map(item => item.id);
    
    await CartItem.destroy({
      where: { id: inactiveItemIds }
    });
    
    await cart.calculateTotals();
    cart.items = activeItems;
  }

  res.json({
    success: true,
    data: { cart }
  });
}));

// @desc    Add item to cart
// @route   POST /api/cart/items
// @access  Private
router.post('/items', authenticateToken, cartValidations.addItem, asyncHandler(async (req, res) => {
  const { product_id, quantity = 1, options = {} } = req.body;

  // Get or create cart
  const cart = await Cart.findOrCreateByUser(req.user.id);

  try {
    const cartItem = await cart.addItem(product_id, quantity, options);
    
    // Reload cart with items
    await cart.reload({
      include: [{
        model: CartItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'price', 'images', 'stock_quantity']
        }]
      }]
    });

    res.json({
      success: true,
      message: 'Item added to cart',
      data: { cart }
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

// @desc    Update cart item quantity
// @route   PUT /api/cart/items/:productId
// @access  Private
router.put('/items/:productId', authenticateToken, commonValidations.id('productId'), cartValidations.updateItem, asyncHandler(async (req, res) => {
  const { quantity } = req.body;
  const productId = req.params.productId;

  const cart = await Cart.findByUser(req.user.id);
  if (!cart) {
    return res.status(404).json({
      success: false,
      message: 'Cart not found'
    });
  }

  try {
    await cart.updateItemQuantity(productId, quantity);
    
    // Reload cart with items
    await cart.reload({
      include: [{
        model: CartItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'price', 'images', 'stock_quantity']
        }]
      }]
    });

    res.json({
      success: true,
      message: 'Cart item updated',
      data: { cart }
    });
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

// @desc    Remove item from cart
// @route   DELETE /api/cart/items/:productId
// @access  Private
router.delete('/items/:productId', authenticateToken, commonValidations.id('productId'), asyncHandler(async (req, res) => {
  const productId = req.params.productId;

  const cart = await Cart.findByUser(req.user.id);
  if (!cart) {
    return res.status(404).json({
      success: false,
      message: 'Cart not found'
    });
  }

  const removed = await cart.removeItem(productId);
  if (!removed) {
    return res.status(404).json({
      success: false,
      message: 'Item not found in cart'
    });
  }

  // Reload cart with items
  await cart.reload({
    include: [{
      model: CartItem,
      as: 'items',
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'price', 'images', 'stock_quantity']
      }]
    }]
  });

  res.json({
    success: true,
    message: 'Item removed from cart',
    data: { cart }
  });
}));

// @desc    Clear cart
// @route   DELETE /api/cart
// @access  Private
router.delete('/', authenticateToken, asyncHandler(async (req, res) => {
  const cart = await Cart.findByUser(req.user.id);
  if (!cart) {
    return res.json({
      success: true,
      message: 'Cart is already empty'
    });
  }

  await cart.clear();

  res.json({
    success: true,
    message: 'Cart cleared successfully',
    data: {
      cart: {
        items: [],
        total_items: 0,
        total_amount: 0
      }
    }
  });
}));

// @desc    Get cart summary
// @route   GET /api/cart/summary
// @access  Private
router.get('/summary', authenticateToken, asyncHandler(async (req, res) => {
  const cart = await Cart.findByUser(req.user.id);
  
  if (!cart) {
    return res.json({
      success: true,
      data: {
        total_items: 0,
        total_amount: 0,
        item_count: 0
      }
    });
  }

  res.json({
    success: true,
    data: {
      total_items: cart.total_items,
      total_amount: cart.total_amount,
      item_count: cart.total_items
    }
  });
}));

// @desc    Sync cart from frontend (for migration from localStorage)
// @route   POST /api/cart/sync
// @access  Private
router.post('/sync', authenticateToken, asyncHandler(async (req, res) => {
  const { items } = req.body;

  if (!Array.isArray(items)) {
    return res.status(400).json({
      success: false,
      message: 'Items must be an array'
    });
  }

  const cart = await Cart.findOrCreateByUser(req.user.id);

  // Clear existing cart
  await cart.clear();

  // Add items from frontend
  for (const item of items) {
    try {
      await cart.addItem(item.product_id || item.id, item.quantity || 1, item.options || {});
    } catch (error) {
      // Skip items that can't be added (out of stock, etc.)
      console.warn(`Failed to sync cart item ${item.product_id || item.id}:`, error.message);
    }
  }

  // Reload cart with items
  await cart.reload({
    include: [{
      model: CartItem,
      as: 'items',
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'name', 'price', 'images', 'stock_quantity']
      }]
    }]
  });

  res.json({
    success: true,
    message: 'Cart synced successfully',
    data: { cart }
  });
}));

module.exports = router;
