const express = require('express');
const { Category, Product } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { categoryValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
router.get('/', asyncHandler(async (req, res) => {
  const { include_products = false, hierarchy = false } = req.query;

  if (hierarchy === 'true') {
    const categoryHierarchy = await Category.getHierarchy();
    return res.json({
      success: true,
      data: { categories: categoryHierarchy }
    });
  }

  const includeOptions = [];
  
  if (include_products === 'true') {
    includeOptions.push({
      model: Product,
      as: 'products',
      where: { status: 'active' },
      required: false,
      attributes: ['id', 'name', 'price', 'images', 'rating']
    });
  }

  const categories = await Category.findActive();

  res.json({
    success: true,
    data: { categories }
  });
}));

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Public
router.get('/:id', commonValidations.id(), asyncHandler(async (req, res) => {
  const category = await Category.findOne({
    where: { 
      id: req.params.id,
      status: 'active'
    },
    include: [
      {
        model: Category,
        as: 'subcategories',
        where: { status: 'active' },
        required: false
      },
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'name', 'slug']
      }
    ]
  });

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  res.json({
    success: true,
    data: { category }
  });
}));

// @desc    Get category by slug
// @route   GET /api/categories/slug/:slug
// @access  Public
router.get('/slug/:slug', asyncHandler(async (req, res) => {
  const category = await Category.findBySlug(req.params.slug);

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Include subcategories
  await category.reload({
    include: [{
      model: Category,
      as: 'subcategories',
      where: { status: 'active' },
      required: false
    }]
  });

  res.json({
    success: true,
    data: { category }
  });
}));

// @desc    Get category products
// @route   GET /api/categories/:id/products
// @access  Public
router.get('/:id/products', commonValidations.id(), asyncHandler(async (req, res) => {
  const { page = 1, limit = 12, sort = 'created_at', order = 'DESC' } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  // Verify category exists
  const category = await Category.findOne({
    where: { 
      id: req.params.id,
      status: 'active'
    }
  });

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Build order clause
  const orderClause = [];
  const validSortFields = ['name', 'price', 'rating', 'created_at', 'sales_count'];
  const sortField = validSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  orderClause.push([sortField, sortOrder]);

  const { count, rows: products } = await Product.findAndCountAll({
    where: {
      category_id: req.params.id,
      status: 'active'
    },
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name', 'slug']
    }],
    order: orderClause,
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      category,
      products,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Create category
// @route   POST /api/categories
// @access  Private/Admin
router.post('/', authenticateToken, requireAdmin, categoryValidations.create, asyncHandler(async (req, res) => {
  const category = await Category.create(req.body);

  res.status(201).json({
    success: true,
    message: 'Category created successfully',
    data: { category }
  });
}));

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private/Admin
router.put('/:id', authenticateToken, requireAdmin, commonValidations.id(), asyncHandler(async (req, res) => {
  const category = await Category.findByPk(req.params.id);

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  await category.update(req.body);

  res.json({
    success: true,
    message: 'Category updated successfully',
    data: { category }
  });
}));

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private/Admin
router.delete('/:id', authenticateToken, requireAdmin, commonValidations.id(), asyncHandler(async (req, res) => {
  const category = await Category.findByPk(req.params.id);

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Check if category has products
  const productCount = await Product.count({
    where: { category_id: req.params.id }
  });

  if (productCount > 0) {
    return res.status(400).json({
      success: false,
      message: 'Cannot delete category with existing products'
    });
  }

  // Check if category has subcategories
  const subcategoryCount = await Category.count({
    where: { parent_id: req.params.id }
  });

  if (subcategoryCount > 0) {
    return res.status(400).json({
      success: false,
      message: 'Cannot delete category with subcategories'
    });
  }

  await category.destroy();

  res.json({
    success: true,
    message: 'Category deleted successfully'
  });
}));

// @desc    Get featured categories
// @route   GET /api/categories/featured
// @access  Public
router.get('/featured/list', asyncHandler(async (req, res) => {
  const categories = await Category.findFeatured();

  res.json({
    success: true,
    data: { categories }
  });
}));

module.exports = router;
