const express = require('express');
const { Favorite, Product, Category } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Get user's favorites
// @route   GET /api/favorites
// @access  Private
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 12 } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows: favorites } = await Favorite.findAndCountAll({
    where: { user_id: req.user.id },
    include: [{
      model: Product,
      as: 'product',
      where: { status: 'active' },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      }]
    }],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      favorites,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Add/Remove product from favorites
// @route   POST /api/favorites/:productId
// @access  Private
router.post('/:productId', authenticateToken, commonValidations.id('productId'), asyncHandler(async (req, res) => {
  const productId = req.params.productId;

  // Check if product exists and is active
  const product = await Product.findOne({
    where: { 
      id: productId,
      status: 'active'
    }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  const result = await Favorite.toggle(req.user.id, productId);

  res.json({
    success: true,
    message: result.action === 'added' ? 'Product added to favorites' : 'Product removed from favorites',
    data: {
      action: result.action,
      is_favorite: result.action === 'added'
    }
  });
}));

// @desc    Remove product from favorites
// @route   DELETE /api/favorites/:productId
// @access  Private
router.delete('/:productId', authenticateToken, commonValidations.id('productId'), asyncHandler(async (req, res) => {
  const productId = req.params.productId;

  const favorite = await Favorite.findOne({
    where: {
      user_id: req.user.id,
      product_id: productId
    }
  });

  if (!favorite) {
    return res.status(404).json({
      success: false,
      message: 'Product not in favorites'
    });
  }

  await favorite.destroy();

  res.json({
    success: true,
    message: 'Product removed from favorites'
  });
}));

// @desc    Check if product is in favorites
// @route   GET /api/favorites/:productId/status
// @access  Private
router.get('/:productId/status', authenticateToken, commonValidations.id('productId'), asyncHandler(async (req, res) => {
  const productId = req.params.productId;

  const isFavorite = await Favorite.isFavorite(req.user.id, productId);

  res.json({
    success: true,
    data: {
      is_favorite: isFavorite
    }
  });
}));

// @desc    Get favorites count
// @route   GET /api/favorites/count
// @access  Private
router.get('/count', authenticateToken, asyncHandler(async (req, res) => {
  const count = await Favorite.count({
    where: { user_id: req.user.id },
    include: [{
      model: Product,
      as: 'product',
      where: { status: 'active' }
    }]
  });

  res.json({
    success: true,
    data: { count }
  });
}));

// @desc    Clear all favorites
// @route   DELETE /api/favorites
// @access  Private
router.delete('/', authenticateToken, asyncHandler(async (req, res) => {
  const deletedCount = await Favorite.destroy({
    where: { user_id: req.user.id }
  });

  res.json({
    success: true,
    message: `${deletedCount} favorites cleared`,
    data: { deleted_count: deletedCount }
  });
}));

module.exports = router;
