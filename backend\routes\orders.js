const express = require('express');
const { Order, OrderItem, Product, Cart, CartItem, Address, User } = require('../models');
const { authenticateToken, requireAdmin, requireOwnershipOrAdmin } = require('../middleware/auth');
const { orderValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const { sequelize } = require('../models');

const router = express.Router();

// @desc    Create order from cart
// @route   POST /api/orders
// @access  Private
router.post('/', authenticateToken, orderValidations.create, asyncHandler(async (req, res) => {
  const { shipping_address_id, billing_address_id, payment_method, notes } = req.body;

  // Get user's cart
  const cart = await Cart.findOne({
    where: { user_id: req.user.id },
    include: [{
      model: CartItem,
      as: 'items',
      include: [{
        model: Product,
        as: 'product'
      }]
    }]
  });

  if (!cart || cart.items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Cart is empty'
    });
  }

  // Validate addresses
  if (shipping_address_id) {
    const shippingAddress = await Address.findOne({
      where: { id: shipping_address_id, user_id: req.user.id }
    });
    if (!shippingAddress) {
      return res.status(400).json({
        success: false,
        message: 'Invalid shipping address'
      });
    }
  }

  if (billing_address_id) {
    const billingAddress = await Address.findOne({
      where: { id: billing_address_id, user_id: req.user.id }
    });
    if (!billingAddress) {
      return res.status(400).json({
        success: false,
        message: 'Invalid billing address'
      });
    }
  }

  // Start transaction
  const transaction = await sequelize.transaction();

  try {
    // Validate stock and calculate totals
    let subtotal = 0;
    const orderItems = [];

    for (const cartItem of cart.items) {
      const product = cartItem.product;
      
      if (product.status !== 'active') {
        throw new Error(`Product ${product.name} is no longer available`);
      }
      
      if (product.stock_quantity < cartItem.quantity) {
        throw new Error(`Insufficient stock for ${product.name}. Available: ${product.stock_quantity}`);
      }

      const itemTotal = parseFloat(product.price) * cartItem.quantity;
      subtotal += itemTotal;

      orderItems.push({
        product_id: product.id,
        product_name: product.name,
        product_sku: product.sku,
        quantity: cartItem.quantity,
        unit_price: product.price,
        total_price: itemTotal,
        options: cartItem.options,
        product_snapshot: {
          name: product.name,
          description: product.description,
          images: product.images,
          brand: product.brand
        }
      });
    }

    // Calculate totals (simplified - in production you'd calculate tax, shipping, etc.)
    const taxAmount = 0; // TODO: Implement tax calculation
    const shippingAmount = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
    const discountAmount = 0; // TODO: Implement discount codes
    const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

    // Create order
    const order = await Order.create({
      user_id: req.user.id,
      subtotal,
      tax_amount: taxAmount,
      shipping_amount: shippingAmount,
      discount_amount: discountAmount,
      total_amount: totalAmount,
      shipping_address_id,
      billing_address_id,
      payment_method,
      notes,
      status: 'pending',
      payment_status: 'pending'
    }, { transaction });

    // Create order items and update product stock
    for (const itemData of orderItems) {
      await OrderItem.create({
        order_id: order.id,
        ...itemData
      }, { transaction });

      // Update product stock
      await Product.decrement('stock_quantity', {
        by: itemData.quantity,
        where: { id: itemData.product_id },
        transaction
      });

      // Update product sales count
      await Product.increment('sales_count', {
        by: itemData.quantity,
        where: { id: itemData.product_id },
        transaction
      });
    }

    // Clear cart
    await cart.clear();

    // Update user stats
    await User.increment('stats.total_orders', {
      by: 1,
      where: { id: req.user.id },
      transaction
    });

    await transaction.commit();

    // Reload order with associations
    await order.reload({
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [{
            model: Product,
            as: 'product',
            attributes: ['id', 'name', 'images']
          }]
        },
        {
          model: Address,
          as: 'shippingAddress'
        },
        {
          model: Address,
          as: 'billingAddress'
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: { order }
    });

  } catch (error) {
    await transaction.rollback();
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
}));

// @desc    Get user's orders
// @route   GET /api/orders
// @access  Private
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const where = { user_id: req.user.id };
  if (status) {
    where.status = status;
  }

  const { count, rows: orders } = await Order.findAndCountAll({
    where,
    include: [
      {
        model: OrderItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'images']
        }]
      },
      {
        model: Address,
        as: 'shippingAddress'
      }
    ],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private (own order or admin)
router.get('/:id', authenticateToken, commonValidations.id(), asyncHandler(async (req, res) => {
  const order = await Order.findOne({
    where: { id: req.params.id },
    include: [
      {
        model: OrderItem,
        as: 'items',
        include: [{
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'images', 'price']
        }]
      },
      {
        model: Address,
        as: 'shippingAddress'
      },
      {
        model: Address,
        as: 'billingAddress'
      },
      {
        model: User,
        as: 'user',
        attributes: ['id', 'first_name', 'last_name', 'email']
      }
    ]
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  // Check ownership (users can only see their own orders, admins can see all)
  if (!req.user.isAdmin() && order.user_id !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }

  res.json({
    success: true,
    data: { order }
  });
}));

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private (own order only)
router.put('/:id/cancel', authenticateToken, commonValidations.id(), asyncHandler(async (req, res) => {
  const { reason } = req.body;

  const order = await Order.findOne({
    where: { 
      id: req.params.id,
      user_id: req.user.id
    },
    include: [{
      model: OrderItem,
      as: 'items'
    }]
  });

  if (!order) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }

  if (!order.canBeCancelled()) {
    return res.status(400).json({
      success: false,
      message: 'Order cannot be cancelled at this stage'
    });
  }

  const transaction = await sequelize.transaction();

  try {
    // Restore product stock
    for (const item of order.items) {
      await Product.increment('stock_quantity', {
        by: item.quantity,
        where: { id: item.product_id },
        transaction
      });
    }

    // Update order status
    await order.updateStatus('cancelled', reason);

    await transaction.commit();

    res.json({
      success: true,
      message: 'Order cancelled successfully',
      data: { order }
    });

  } catch (error) {
    await transaction.rollback();
    return res.status(500).json({
      success: false,
      message: 'Failed to cancel order'
    });
  }
}));

module.exports = router;
