const express = require('express');
const { Op } = require('sequelize');
const { Product, Category, Review, Favorite } = require('../models');
const { authenticateToken, optionalAuth, requireAdmin } = require('../middleware/auth');
const { productValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Get all products with filtering, sorting, and pagination
// @route   GET /api/products
// @access  Public
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 12,
    category,
    search,
    sort = 'created_at',
    order = 'DESC',
    min_price,
    max_price,
    on_sale,
    featured,
    in_stock
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  
  // Build where clause
  const where = { status: 'active' };
  
  if (category) {
    if (isNaN(category)) {
      // Category slug provided
      const categoryRecord = await Category.findBySlug(category);
      if (categoryRecord) {
        where.category_id = categoryRecord.id;
      }
    } else {
      // Category ID provided
      where.category_id = parseInt(category);
    }
  }
  
  if (search) {
    where[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { brand: { [Op.iLike]: `%${search}%` } }
    ];
  }
  
  if (min_price || max_price) {
    where.price = {};
    if (min_price) where.price[Op.gte] = parseFloat(min_price);
    if (max_price) where.price[Op.lte] = parseFloat(max_price);
  }
  
  if (on_sale === 'true') {
    where.on_sale = true;
  }
  
  if (featured === 'true') {
    where.featured = true;
  }
  
  if (in_stock === 'true') {
    where.stock_quantity = { [Op.gt]: 0 };
  }

  // Build order clause
  const orderClause = [];
  const validSortFields = ['name', 'price', 'rating', 'created_at', 'sales_count'];
  const sortField = validSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  orderClause.push([sortField, sortOrder]);

  const { count, rows: products } = await Product.findAndCountAll({
    where,
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      }
    ],
    order: orderClause,
    limit: parseInt(limit),
    offset,
    distinct: true
  });

  // Add favorite status if user is authenticated
  if (req.user) {
    const productIds = products.map(p => p.id);
    const favorites = await Favorite.findAll({
      where: {
        user_id: req.user.id,
        product_id: { [Op.in]: productIds }
      }
    });
    
    const favoriteIds = new Set(favorites.map(f => f.product_id));
    products.forEach(product => {
      product.dataValues.is_favorite = favoriteIds.has(product.id);
    });
  }

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      products,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }
  });
}));

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
router.get('/:id', optionalAuth, commonValidations.id(), asyncHandler(async (req, res) => {
  const product = await Product.findOne({
    where: { 
      id: req.params.id,
      status: 'active'
    },
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      },
      {
        model: Review,
        as: 'reviews',
        where: { status: 'approved' },
        required: false,
        include: [{
          model: require('../models').User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'avatar']
        }],
        order: [['created_at', 'DESC']],
        limit: 10
      }
    ]
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  // Increment view count
  await product.incrementViewCount();

  // Check if user has favorited this product
  if (req.user) {
    const favorite = await Favorite.findOne({
      where: {
        user_id: req.user.id,
        product_id: product.id
      }
    });
    product.dataValues.is_favorite = !!favorite;
  }

  res.json({
    success: true,
    data: { product }
  });
}));

// @desc    Create product
// @route   POST /api/products
// @access  Private/Admin
router.post('/', authenticateToken, requireAdmin, productValidations.create, asyncHandler(async (req, res) => {
  const product = await Product.create(req.body);
  
  await product.reload({
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name', 'slug']
    }]
  });

  res.status(201).json({
    success: true,
    message: 'Product created successfully',
    data: { product }
  });
}));

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private/Admin
router.put('/:id', authenticateToken, requireAdmin, commonValidations.id(), productValidations.update, asyncHandler(async (req, res) => {
  const product = await Product.findByPk(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  await product.update(req.body);
  
  await product.reload({
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name', 'slug']
    }]
  });

  res.json({
    success: true,
    message: 'Product updated successfully',
    data: { product }
  });
}));

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private/Admin
router.delete('/:id', authenticateToken, requireAdmin, commonValidations.id(), asyncHandler(async (req, res) => {
  const product = await Product.findByPk(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  // Soft delete by setting status to archived
  await product.update({ status: 'archived' });

  res.json({
    success: true,
    message: 'Product deleted successfully'
  });
}));

// @desc    Get featured products
// @route   GET /api/products/featured
// @access  Public
router.get('/featured/list', optionalAuth, asyncHandler(async (req, res) => {
  const { limit = 8 } = req.query;

  const products = await Product.findAll({
    where: {
      status: 'active',
      featured: true
    },
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name', 'slug']
    }],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit)
  });

  // Add favorite status if user is authenticated
  if (req.user) {
    const productIds = products.map(p => p.id);
    const favorites = await Favorite.findAll({
      where: {
        user_id: req.user.id,
        product_id: { [Op.in]: productIds }
      }
    });
    
    const favoriteIds = new Set(favorites.map(f => f.product_id));
    products.forEach(product => {
      product.dataValues.is_favorite = favoriteIds.has(product.id);
    });
  }

  res.json({
    success: true,
    data: { products }
  });
}));

// @desc    Get products on sale
// @route   GET /api/products/sale
// @access  Public
router.get('/sale/list', optionalAuth, asyncHandler(async (req, res) => {
  const { page = 1, limit = 12 } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows: products } = await Product.findAndCountAll({
    where: {
      status: 'active',
      on_sale: true
    },
    include: [{
      model: Category,
      as: 'category',
      attributes: ['id', 'name', 'slug']
    }],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit),
    offset
  });

  // Add favorite status if user is authenticated
  if (req.user) {
    const productIds = products.map(p => p.id);
    const favorites = await Favorite.findAll({
      where: {
        user_id: req.user.id,
        product_id: { [Op.in]: productIds }
      }
    });
    
    const favoriteIds = new Set(favorites.map(f => f.product_id));
    products.forEach(product => {
      product.dataValues.is_favorite = favoriteIds.has(product.id);
    });
  }

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      products,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

module.exports = router;
