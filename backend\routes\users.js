const express = require('express');
const { User, Order, Favorite, Address } = require('../models');
const { authenticateToken, requireOwnershipOrAdmin } = require('../middleware/auth');
const { userValidations, commonValidations } = require('../utils/validation');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// @desc    Get user profile
// @route   GET /api/users/:id
// @access  Private (own profile or admin)
router.get('/:id', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.params.id, {
    include: [
      {
        model: Address,
        as: 'addresses',
        where: { is_default: true },
        required: false
      }
    ]
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  res.json({
    success: true,
    data: { user }
  });
}));

// @desc    Update user profile
// @route   PUT /api/users/:id
// @access  Private (own profile or admin)
router.put('/:id', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), userValidations.updateProfile, asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const allowedFields = ['first_name', 'last_name', 'phone', 'preferences'];
  const updateData = {};
  
  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      updateData[field] = req.body[field];
    }
  });

  await user.update(updateData);

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: { user }
  });
}));

// @desc    Change password
// @route   PUT /api/users/:id/password
// @access  Private (own profile only)
router.put('/:id/password', authenticateToken, commonValidations.id(), userValidations.changePassword, asyncHandler(async (req, res) => {
  const { current_password, password } = req.body;

  // Only allow users to change their own password
  if (parseInt(req.params.id) !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: 'You can only change your own password'
    });
  }

  const user = await User.findOne({
    where: { id: req.params.id },
    attributes: { include: ['password'] }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Verify current password
  const isCurrentPasswordValid = await user.validatePassword(current_password);
  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      success: false,
      message: 'Current password is incorrect'
    });
  }

  // Update password
  await user.update({ password });

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
}));

// @desc    Get user orders
// @route   GET /api/users/:id/orders
// @access  Private (own orders or admin)
router.get('/:id/orders', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const where = { user_id: req.params.id };
  if (status) {
    where.status = status;
  }

  const { count, rows: orders } = await Order.findAndCountAll({
    where,
    include: [
      {
        model: require('../models').OrderItem,
        as: 'items',
        include: [{
          model: require('../models').Product,
          as: 'product',
          attributes: ['id', 'name', 'images']
        }]
      },
      {
        model: Address,
        as: 'shippingAddress'
      }
    ],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Get user favorites
// @route   GET /api/users/:id/favorites
// @access  Private (own favorites or admin)
router.get('/:id/favorites', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const { page = 1, limit = 12 } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows: favorites } = await Favorite.findAndCountAll({
    where: { user_id: req.params.id },
    include: [{
      model: require('../models').Product,
      as: 'product',
      where: { status: 'active' },
      include: [{
        model: require('../models').Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      }]
    }],
    order: [['created_at', 'DESC']],
    limit: parseInt(limit),
    offset
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  res.json({
    success: true,
    data: {
      favorites,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
}));

// @desc    Get user addresses
// @route   GET /api/users/:id/addresses
// @access  Private (own addresses or admin)
router.get('/:id/addresses', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const addresses = await Address.findByUser(req.params.id);

  res.json({
    success: true,
    data: { addresses }
  });
}));

// @desc    Get user statistics
// @route   GET /api/users/:id/stats
// @access  Private (own stats or admin)
router.get('/:id/stats', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const userId = req.params.id;

  // Get order statistics
  const orderStats = await Order.findAll({
    where: { 
      user_id: userId,
      status: 'delivered'
    },
    attributes: [
      [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'total_orders'],
      [require('sequelize').fn('SUM', require('sequelize').col('total_amount')), 'total_spent'],
      [require('sequelize').fn('AVG', require('sequelize').col('total_amount')), 'average_order_value']
    ],
    raw: true
  });

  // Get favorite count
  const favoriteCount = await Favorite.count({
    where: { user_id: userId }
  });

  // Get recent order
  const recentOrder = await Order.findOne({
    where: { user_id: userId },
    order: [['created_at', 'DESC']],
    attributes: ['id', 'order_number', 'status', 'total_amount', 'created_at']
  });

  const stats = {
    total_orders: parseInt(orderStats[0].total_orders) || 0,
    total_spent: parseFloat(orderStats[0].total_spent) || 0,
    average_order_value: parseFloat(orderStats[0].average_order_value) || 0,
    favorite_items: favoriteCount,
    recent_order: recentOrder
  };

  res.json({
    success: true,
    data: { stats }
  });
}));

// @desc    Delete user account
// @route   DELETE /api/users/:id
// @access  Private (own account or admin)
router.delete('/:id', authenticateToken, commonValidations.id(), requireOwnershipOrAdmin('id'), asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Soft delete by setting status to inactive
  await user.update({ status: 'inactive' });

  res.json({
    success: true,
    message: 'Account deactivated successfully'
  });
}));

module.exports = router;
