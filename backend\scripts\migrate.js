const { sequelize } = require('../models');
const logger = require('../utils/logger');

const runMigrations = async () => {
  try {
    logger.info('Starting database migrations...');

    // Test database connection
    await sequelize.authenticate();
    logger.info('Database connection established successfully.');

    // Sync all models
    await sequelize.sync({ alter: true });
    logger.info('Database models synchronized successfully.');

    logger.info('Database migrations completed successfully!');

  } catch (error) {
    logger.error('Database migration failed:', error);
    throw error;
  }
};

// Run migrations if called directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migrations failed:', error);
      process.exit(1);
    });
}

module.exports = runMigrations;
