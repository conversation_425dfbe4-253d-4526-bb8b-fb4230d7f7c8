const { sequelize, User, Category, Product, Order, OrderItem, Address } = require('../models');
const logger = require('../utils/logger');

const seedDatabase = async () => {
  try {
    logger.info('Starting database seeding...');

    // Clear existing data (in development only)
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ force: true });
      logger.info('Database tables recreated');
    }

    // Seed Categories
    const categories = await Category.bulkCreate([
      {
        name: 'Women',
        slug: 'women',
        description: 'Fashion for women',
        featured: true,
        sort_order: 1
      },
      {
        name: 'Men',
        slug: 'men',
        description: 'Fashion for men',
        featured: true,
        sort_order: 2
      },
      {
        name: 'Dress<PERSON>',
        slug: 'dresses',
        description: 'Women\'s dresses',
        parent_id: 1,
        sort_order: 1
      },
      {
        name: 'Tops',
        slug: 'tops',
        description: 'Women\'s tops and blouses',
        parent_id: 1,
        sort_order: 2
      },
      {
        name: 'Shi<PERSON>',
        slug: 'shirts',
        description: 'Men\'s shirts',
        parent_id: 2,
        sort_order: 1
      },
      {
        name: 'Pants',
        slug: 'pants',
        description: 'Men\'s pants and trousers',
        parent_id: 2,
        sort_order: 2
      },
      {
        name: 'Accessories',
        slug: 'accessories',
        description: 'Fashion accessories',
        featured: true,
        sort_order: 3
      },
      {
        name: 'Shoes',
        slug: 'shoes',
        description: 'Footwear collection',
        featured: true,
        sort_order: 4
      }
    ]);

    logger.info(`Created ${categories.length} categories`);

    // Seed Users
    const users = await User.bulkCreate([
      {
        email: '<EMAIL>',
        password: 'admin123',
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin',
        phone: '+1234567890',
        email_verified: true
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        role: 'customer',
        phone: '+1234567891',
        email_verified: true
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Jane',
        last_name: 'Smith',
        role: 'customer',
        phone: '+1234567892',
        email_verified: true
      }
    ]);

    logger.info(`Created ${users.length} users`);

    // Seed Products
    const products = await Product.bulkCreate([
      {
        name: 'Summer Floral Dress',
        description: 'Beautiful floral dress perfect for summer occasions',
        sku: 'VTH-DRS-001',
        price: 29.99,
        original_price: 49.99,
        category_id: 3, // Dresses
        brand: 'VAITH',
        stock_quantity: 50,
        on_sale: true,
        featured: true,
        images: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          sizes: ['XS', 'S', 'M', 'L', 'XL'],
          colors: ['Floral Blue', 'Floral Pink']
        },
        tags: ['summer', 'floral', 'dress', 'casual'],
        rating: 4.5,
        review_count: 128
      },
      {
        name: 'Classic White Shirt',
        description: 'Timeless white shirt for professional and casual wear',
        sku: 'VTH-SHT-001',
        price: 24.99,
        original_price: 34.99,
        category_id: 5, // Shirts
        brand: 'VAITH',
        stock_quantity: 75,
        on_sale: true,
        featured: true,
        images: ['https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          sizes: ['S', 'M', 'L', 'XL', 'XXL'],
          colors: ['White', 'Light Blue']
        },
        tags: ['shirt', 'classic', 'professional', 'cotton'],
        rating: 4.2,
        review_count: 89
      },
      {
        name: 'Denim Jacket',
        description: 'Stylish denim jacket for layering',
        sku: 'VTH-JKT-001',
        price: 59.99,
        category_id: 4, // Tops
        brand: 'VAITH',
        stock_quantity: 30,
        featured: true,
        images: ['https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          sizes: ['XS', 'S', 'M', 'L', 'XL'],
          colors: ['Light Denim', 'Dark Denim']
        },
        tags: ['jacket', 'denim', 'casual', 'layering'],
        rating: 4.7,
        review_count: 203
      },
      {
        name: 'Casual Sneakers',
        description: 'Comfortable sneakers for everyday wear',
        sku: 'VTH-SNK-001',
        price: 79.99,
        category_id: 8, // Shoes
        brand: 'VAITH',
        stock_quantity: 40,
        images: ['https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          sizes: ['6', '7', '8', '9', '10', '11', '12'],
          colors: ['White', 'Black', 'Gray']
        },
        tags: ['sneakers', 'casual', 'comfortable', 'everyday'],
        rating: 4.4,
        review_count: 156
      },
      {
        name: 'Leather Handbag',
        description: 'Premium leather handbag with multiple compartments',
        sku: 'VTH-BAG-001',
        price: 129.99,
        original_price: 179.99,
        category_id: 7, // Accessories
        brand: 'VAITH',
        stock_quantity: 25,
        on_sale: true,
        images: ['https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          colors: ['Black', 'Brown', 'Tan']
        },
        tags: ['handbag', 'leather', 'premium', 'accessories'],
        rating: 4.8,
        review_count: 92
      },
      {
        name: 'Slim Fit Jeans',
        description: 'Modern slim fit jeans with stretch comfort',
        sku: 'VTH-JNS-001',
        price: 49.99,
        category_id: 6, // Pants
        brand: 'VAITH',
        stock_quantity: 60,
        images: ['https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=500&fit=crop&auto=format&q=80'],
        variants: {
          sizes: ['28', '30', '32', '34', '36', '38'],
          colors: ['Dark Blue', 'Light Blue', 'Black']
        },
        tags: ['jeans', 'slim fit', 'denim', 'stretch'],
        rating: 4.3,
        review_count: 174
      }
    ]);

    logger.info(`Created ${products.length} products`);

    // Seed Addresses
    const addresses = await Address.bulkCreate([
      {
        user_id: 2,
        type: 'both',
        first_name: 'John',
        last_name: 'Doe',
        address_line_1: '123 Main Street',
        city: 'New York',
        state: 'NY',
        postal_code: '10001',
        country: 'US',
        phone: '+1234567891',
        is_default: true
      },
      {
        user_id: 3,
        type: 'both',
        first_name: 'Jane',
        last_name: 'Smith',
        address_line_1: '456 Oak Avenue',
        city: 'Los Angeles',
        state: 'CA',
        postal_code: '90210',
        country: 'US',
        phone: '+1234567892',
        is_default: true
      }
    ]);

    logger.info(`Created ${addresses.length} addresses`);

    // Seed Sample Orders
    const orders = await Order.bulkCreate([
      {
        user_id: 2,
        order_number: 'ORD-001-2024',
        status: 'delivered',
        payment_status: 'paid',
        payment_method: 'credit_card',
        subtotal: 89.98,
        tax_amount: 7.20,
        shipping_amount: 0,
        total_amount: 97.18,
        shipping_address_id: 1,
        billing_address_id: 1,
        shipped_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        delivered_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
      },
      {
        user_id: 3,
        order_number: 'ORD-002-2024',
        status: 'shipped',
        payment_status: 'paid',
        payment_method: 'paypal',
        subtotal: 129.99,
        tax_amount: 10.40,
        shipping_amount: 9.99,
        total_amount: 150.38,
        shipping_address_id: 2,
        billing_address_id: 2,
        shipped_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      }
    ]);

    logger.info(`Created ${orders.length} orders`);

    // Seed Order Items
    const orderItems = await OrderItem.bulkCreate([
      {
        order_id: 1,
        product_id: 1,
        product_name: 'Summer Floral Dress',
        product_sku: 'VTH-DRS-001',
        quantity: 1,
        unit_price: 29.99,
        total_price: 29.99,
        options: { size: 'M', color: 'Floral Blue' }
      },
      {
        order_id: 1,
        product_id: 3,
        product_name: 'Denim Jacket',
        product_sku: 'VTH-JKT-001',
        quantity: 1,
        unit_price: 59.99,
        total_price: 59.99,
        options: { size: 'M', color: 'Light Denim' }
      },
      {
        order_id: 2,
        product_id: 5,
        product_name: 'Leather Handbag',
        product_sku: 'VTH-BAG-001',
        quantity: 1,
        unit_price: 129.99,
        total_price: 129.99,
        options: { color: 'Black' }
      }
    ]);

    logger.info(`Created ${orderItems.length} order items`);

    logger.info('Database seeding completed successfully!');

  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
};

// Run seeding if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      logger.info('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedDatabase;
