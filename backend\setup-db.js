const { sequelize } = require('./models');
const logger = require('./utils/logger');

// Import seed data
const categories = [
  { name: 'Women', slug: 'women', description: 'Women\'s fashion and accessories' },
  { name: 'Men', slug: 'men', description: 'Men\'s fashion and accessories' },
  { name: 'Shoes', slug: 'shoes', description: 'Footwear for all occasions' },
  { name: 'Accessories', slug: 'accessories', description: 'Fashion accessories and jewelry' },
  { name: 'Sale', slug: 'sale', description: 'Discounted items' },
  { name: 'New Arrivals', slug: 'new-arrivals', description: 'Latest fashion trends' },
  { name: 'Bags', slug: 'bags', description: 'Handbags, backpacks, and more' },
  { name: 'Jewelry', slug: 'jewelry', description: 'Rings, necklaces, and accessories' }
];

const bcrypt = require('bcryptjs');

const users = [
  {
    email: '<EMAIL>',
    password: 'admin123', // Will be hashed during creation
    first_name: 'Admin',
    last_name: 'User',
    role: 'admin',
    status: 'active',
    email_verified: true
  },
  {
    email: '<EMAIL>',
    password: 'user123', // Will be hashed during creation
    first_name: 'Test',
    last_name: 'User',
    role: 'customer',
    status: 'active',
    email_verified: true
  },
  {
    email: '<EMAIL>',
    password: 'demo123', // Will be hashed during creation
    first_name: 'Demo',
    last_name: 'User',
    role: 'customer',
    status: 'active',
    email_verified: true
  }
];

const products = [
  {
    name: 'Summer Floral Dress',
    description: 'Beautiful floral dress perfect for summer occasions',
    price: 49.99,
    sale_price: 29.99,
    sku: 'SFD001',
    stock_quantity: 50,
    category_id: 1,
    images: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: true,
    on_sale: true,
    status: 'active'
  },
  {
    name: 'Classic White Shirt',
    description: 'Timeless white shirt for professional and casual wear',
    price: 34.99,
    sale_price: 24.99,
    sku: 'CWS001',
    stock_quantity: 30,
    category_id: 2,
    images: ['https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: true,
    on_sale: true,
    status: 'active'
  },
  {
    name: 'Denim Jacket',
    description: 'Stylish denim jacket for layering',
    price: 59.99,
    sku: 'DJ001',
    stock_quantity: 25,
    category_id: 1,
    images: ['https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: true,
    on_sale: false,
    status: 'active'
  },
  {
    name: 'Casual Sneakers',
    description: 'Comfortable sneakers for everyday wear',
    price: 99.99,
    sale_price: 79.99,
    sku: 'CS001',
    stock_quantity: 40,
    category_id: 3,
    images: ['https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: false,
    on_sale: true,
    status: 'active'
  },
  {
    name: 'Bohemian Maxi Dress',
    description: 'Flowing maxi dress with bohemian style',
    price: 45.99,
    sku: 'BMD001',
    stock_quantity: 20,
    category_id: 1,
    images: ['https://images.unsplash.com/photo-1509631179647-0177331693ae?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: true,
    on_sale: false,
    status: 'active'
  },
  {
    name: 'Leather Handbag',
    description: 'Premium leather handbag with elegant design',
    price: 129.99,
    sale_price: 89.99,
    sku: 'LHB001',
    stock_quantity: 15,
    category_id: 7,
    images: ['https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=500&fit=crop&auto=format&q=80'],
    is_featured: false,
    on_sale: true,
    status: 'active'
  }
];

const setupDatabase = async () => {
  try {
    console.log('🚀 Starting database setup...');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Sync all models (create tables)
    await sequelize.sync({ force: true }); // force: true will drop and recreate tables
    console.log('✅ Database models synchronized successfully.');

    // Import models
    const { Category, User, Product } = require('./models');

    // Create categories
    console.log('📂 Creating categories...');
    for (const categoryData of categories) {
      await Category.create(categoryData);
    }
    console.log(`✅ Created ${categories.length} categories`);

    // Create users
    console.log('👥 Creating users...');
    for (const userData of users) {
      // Hash the password before creating the user
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      const userWithHashedPassword = { ...userData, password: hashedPassword };
      await User.create(userWithHashedPassword);
    }
    console.log(`✅ Created ${users.length} users`);

    // Create products
    console.log('🛍️ Creating products...');
    for (const productData of products) {
      await Product.create(productData);
    }
    console.log(`✅ Created ${products.length} products`);

    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('Demo accounts:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User: <EMAIL> / user123');
    console.log('Demo: <EMAIL> / demo123');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    throw error;
  }
};

// Run setup if called directly
if (require.main === module) {
  setupDatabase()
    .then(() => {
      console.log('Setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Setup failed:', error);
      process.exit(1);
    });
}

module.exports = setupDatabase;
