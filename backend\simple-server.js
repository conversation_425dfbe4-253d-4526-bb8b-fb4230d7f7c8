const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// CORS configuration
const corsOptions = {
  origin: ['http://localhost:8080', 'http://127.0.0.1:8080', 'file://'],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Sample data for testing
const sampleProducts = [
  {
    id: 1,
    name: 'Summer Floral Dress',
    description: 'Beautiful floral dress perfect for summer occasions',
    price: 29.99,
    original_price: 49.99,
    images: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Women',
    on_sale: true,
    featured: true,
    rating: 4.5,
    stock_quantity: 50
  },
  {
    id: 2,
    name: 'Classic White Shirt',
    description: 'Timeless white shirt for professional and casual wear',
    price: 24.99,
    original_price: 34.99,
    images: ['https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Men',
    on_sale: true,
    featured: true,
    rating: 4.2,
    stock_quantity: 75
  },
  {
    id: 3,
    name: 'Denim Jacket',
    description: 'Stylish denim jacket for layering',
    price: 59.99,
    images: ['https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Women',
    featured: true,
    rating: 4.7,
    stock_quantity: 30
  },
  {
    id: 4,
    name: 'Casual Sneakers',
    description: 'Comfortable sneakers for everyday wear',
    price: 79.99,
    images: ['https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Shoes',
    rating: 4.4,
    stock_quantity: 40
  },
  {
    id: 5,
    name: 'Leather Handbag',
    description: 'Premium leather handbag with multiple compartments',
    price: 129.99,
    original_price: 179.99,
    images: ['https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Accessories',
    on_sale: true,
    rating: 4.8,
    stock_quantity: 25
  },
  {
    id: 6,
    name: 'Slim Fit Jeans',
    description: 'Modern slim fit jeans with stretch comfort',
    price: 49.99,
    images: ['https://images.unsplash.com/photo-**********-787c3835535d?w=400&h=500&fit=crop&auto=format&q=80'],
    category: 'Men',
    rating: 4.3,
    stock_quantity: 60
  }
];

const sampleCategories = [
  { id: 1, name: 'Women', slug: 'women', featured: true },
  { id: 2, name: 'Men', slug: 'men', featured: true },
  { id: 3, name: 'Accessories', slug: 'accessories', featured: true },
  { id: 4, name: 'Shoes', slug: 'shoes', featured: true }
];

// Sample user for testing
let currentUser = null;
let authToken = null;

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'VAITH Backend API is running'
  });
});

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    currentUser = {
      id: 1,
      email: '<EMAIL>',
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin'
    };
    authToken = 'sample-jwt-token-admin';
  } else if (email && password) {
    currentUser = {
      id: 2,
      email: email,
      first_name: 'Test',
      last_name: 'User',
      role: 'customer'
    };
    authToken = 'sample-jwt-token-user';
  } else {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: currentUser,
      token: authToken
    }
  });
});

app.post('/api/auth/register', (req, res) => {
  const { email, password, first_name, last_name } = req.body;
  
  currentUser = {
    id: 3,
    email,
    first_name,
    last_name,
    role: 'customer'
  };
  authToken = 'sample-jwt-token-new-user';

  res.status(201).json({
    success: true,
    message: 'Registration successful',
    data: {
      user: currentUser,
      token: authToken
    }
  });
});

app.get('/api/auth/me', (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token || !currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }

  res.json({
    success: true,
    data: { user: currentUser }
  });
});

// Product endpoints
app.get('/api/products', (req, res) => {
  const { category, search, featured, on_sale, limit = 12, page = 1 } = req.query;
  
  let filteredProducts = [...sampleProducts];
  
  if (category) {
    filteredProducts = filteredProducts.filter(p => 
      p.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (search) {
    filteredProducts = filteredProducts.filter(p => 
      p.name.toLowerCase().includes(search.toLowerCase()) ||
      p.description.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  if (featured === 'true') {
    filteredProducts = filteredProducts.filter(p => p.featured);
  }
  
  if (on_sale === 'true') {
    filteredProducts = filteredProducts.filter(p => p.on_sale);
  }

  const startIndex = (parseInt(page) - 1) * parseInt(limit);
  const endIndex = startIndex + parseInt(limit);
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      products: paginatedProducts,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(filteredProducts.length / parseInt(limit)),
        total_items: filteredProducts.length,
        items_per_page: parseInt(limit)
      }
    }
  });
});

app.get('/api/products/featured/list', (req, res) => {
  const featuredProducts = sampleProducts.filter(p => p.featured);
  
  res.json({
    success: true,
    data: { products: featuredProducts }
  });
});

app.get('/api/products/sale/list', (req, res) => {
  const saleProducts = sampleProducts.filter(p => p.on_sale);
  
  res.json({
    success: true,
    data: { products: saleProducts }
  });
});

app.get('/api/products/:id', (req, res) => {
  const product = sampleProducts.find(p => p.id === parseInt(req.params.id));
  
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  res.json({
    success: true,
    data: { product }
  });
});

// Categories
app.get('/api/categories', (req, res) => {
  res.json({
    success: true,
    data: { categories: sampleCategories }
  });
});

// Cart (simple in-memory storage)
let cart = { items: [], total_items: 0, total_amount: 0 };

app.get('/api/cart', (req, res) => {
  res.json({
    success: true,
    data: { cart }
  });
});

app.post('/api/cart/items', (req, res) => {
  const { product_id, quantity = 1 } = req.body;
  const product = sampleProducts.find(p => p.id === parseInt(product_id));
  
  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }

  const existingItem = cart.items.find(item => item.product_id === parseInt(product_id));
  
  if (existingItem) {
    existingItem.quantity += quantity;
  } else {
    cart.items.push({
      id: cart.items.length + 1,
      product_id: parseInt(product_id),
      quantity,
      product
    });
  }

  // Recalculate totals
  cart.total_items = cart.items.reduce((sum, item) => sum + item.quantity, 0);
  cart.total_amount = cart.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);

  res.json({
    success: true,
    message: 'Item added to cart',
    data: { cart }
  });
});

// Favorites (simple in-memory storage)
let favorites = [];

app.get('/api/favorites', (req, res) => {
  const favoriteProducts = favorites.map(fav => ({
    id: fav.id,
    product: sampleProducts.find(p => p.id === fav.product_id)
  })).filter(fav => fav.product);

  res.json({
    success: true,
    data: { favorites: favoriteProducts }
  });
});

app.post('/api/favorites/:productId', (req, res) => {
  const productId = parseInt(req.params.productId);
  const existingIndex = favorites.findIndex(fav => fav.product_id === productId);
  
  if (existingIndex >= 0) {
    favorites.splice(existingIndex, 1);
    res.json({
      success: true,
      message: 'Product removed from favorites',
      data: { action: 'removed', is_favorite: false }
    });
  } else {
    favorites.push({
      id: favorites.length + 1,
      product_id: productId
    });
    res.json({
      success: true,
      message: 'Product added to favorites',
      data: { action: 'added', is_favorite: true }
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 VAITH Backend Server running on port ${PORT}`);
  console.log(`📊 Environment: development`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('Sample credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('User: any email / any password');
});
