/**
 * Simple API Test Script
 * Run this script to test basic API functionality
 * Usage: node test-api.js
 */

const baseURL = 'http://localhost:3000/api';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'Test',
  last_name: 'User',
  phone: '+1234567890'
};

let authToken = '';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${baseURL}${endpoint}`;
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
      ...options.headers
    },
    ...options
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    console.log(`${options.method || 'GET'} ${endpoint}:`, response.status, data.success ? '✅' : '❌');
    
    if (!response.ok) {
      console.log('Error:', data.message);
    }
    
    return { response, data };
  } catch (error) {
    console.log(`${options.method || 'GET'} ${endpoint}:`, '❌', error.message);
    return { error };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('\n🏥 Testing Health Check...');
  await apiRequest('/health');
}

async function testUserRegistration() {
  console.log('\n👤 Testing User Registration...');
  const { data } = await apiRequest('/auth/register', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  if (data && data.success) {
    authToken = data.data.token;
    console.log('Auth token received ✅');
  }
}

async function testUserLogin() {
  console.log('\n🔐 Testing User Login...');
  const { data } = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password
    })
  });
  
  if (data && data.success) {
    authToken = data.data.token;
    console.log('Login successful ✅');
  }
}

async function testGetCurrentUser() {
  console.log('\n👤 Testing Get Current User...');
  await apiRequest('/auth/me');
}

async function testGetProducts() {
  console.log('\n🛍️ Testing Get Products...');
  await apiRequest('/products?limit=5');
}

async function testGetCategories() {
  console.log('\n📂 Testing Get Categories...');
  await apiRequest('/categories');
}

async function testGetFeaturedProducts() {
  console.log('\n⭐ Testing Get Featured Products...');
  await apiRequest('/products/featured/list');
}

async function testCart() {
  console.log('\n🛒 Testing Cart Operations...');
  
  // Get cart
  await apiRequest('/cart');
  
  // Add item to cart (assuming product ID 1 exists)
  await apiRequest('/cart/items', {
    method: 'POST',
    body: JSON.stringify({
      product_id: 1,
      quantity: 2
    })
  });
  
  // Get cart again
  await apiRequest('/cart');
  
  // Update cart item
  await apiRequest('/cart/items/1', {
    method: 'PUT',
    body: JSON.stringify({
      quantity: 3
    })
  });
  
  // Remove item from cart
  await apiRequest('/cart/items/1', {
    method: 'DELETE'
  });
}

async function testFavorites() {
  console.log('\n❤️ Testing Favorites...');
  
  // Get favorites
  await apiRequest('/favorites');
  
  // Add to favorites (assuming product ID 1 exists)
  await apiRequest('/favorites/1', {
    method: 'POST'
  });
  
  // Check favorite status
  await apiRequest('/favorites/1/status');
  
  // Remove from favorites
  await apiRequest('/favorites/1', {
    method: 'DELETE'
  });
}

async function testAdminLogin() {
  console.log('\n👑 Testing Admin Login...');
  const { data } = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    })
  });
  
  if (data && data.success) {
    authToken = data.data.token;
    console.log('Admin login successful ✅');
    return true;
  }
  return false;
}

async function testAdminDashboard() {
  console.log('\n📊 Testing Admin Dashboard...');
  await apiRequest('/admin/dashboard');
}

async function testAdminUsers() {
  console.log('\n👥 Testing Admin Users...');
  await apiRequest('/admin/users?limit=5');
}

async function testAdminOrders() {
  console.log('\n📦 Testing Admin Orders...');
  await apiRequest('/admin/orders?limit=5');
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting API Tests...');
  console.log('Make sure the backend server is running on http://localhost:3000');
  
  try {
    // Basic tests
    await testHealthCheck();
    
    // Authentication tests
    await testUserRegistration();
    await testGetCurrentUser();
    
    // Product tests
    await testGetProducts();
    await testGetCategories();
    await testGetFeaturedProducts();
    
    // User feature tests
    await testCart();
    await testFavorites();
    
    // Admin tests
    const adminLoggedIn = await testAdminLogin();
    if (adminLoggedIn) {
      await testAdminDashboard();
      await testAdminUsers();
      await testAdminOrders();
    }
    
    console.log('\n✅ All tests completed!');
    console.log('\nIf you see any ❌ errors above, check the backend logs for details.');
    
  } catch (error) {
    console.error('\n❌ Test runner failed:', error);
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or you can install node-fetch');
  console.log('Install node-fetch: npm install node-fetch');
  console.log('Then add this line at the top: const fetch = require("node-fetch");');
  process.exit(1);
}

// Run tests
runTests();
