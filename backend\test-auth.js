const bcrypt = require('bcryptjs');
const { User } = require('./models');

async function testAuth() {
  try {
    // Find the demo user
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('✅ User found:', user.email);
    console.log('🔐 Stored password hash:', user.password);
    
    // Test password comparison
    const testPassword = 'demo123';
    const isValid = await bcrypt.compare(testPassword, user.password);
    
    console.log('🧪 Testing password:', testPassword);
    console.log('✅ Password valid:', isValid);
    
    // Create a new hash for comparison
    const newHash = await bcrypt.hash(testPassword, 10);
    console.log('🆕 New hash for same password:', newHash);
    
    const isNewHashValid = await bcrypt.compare(testPassword, newHash);
    console.log('✅ New hash valid:', isNewHashValid);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testAuth().then(() => process.exit(0));
