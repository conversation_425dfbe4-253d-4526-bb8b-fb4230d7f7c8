const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

class Logger {
  constructor() {
    this.logFile = path.join(logsDir, 'app.log');
    this.errorFile = path.join(logsDir, 'error.log');
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaString = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaString}\n`;
  }

  writeToFile(filename, content) {
    try {
      fs.appendFileSync(filename, content);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  log(level, message, meta = {}) {
    const formattedMessage = this.formatMessage(level, message, meta);
    
    // Write to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(formattedMessage.trim());
    }

    // Write to file
    this.writeToFile(this.logFile, formattedMessage);

    // Write errors to separate file
    if (level === 'error') {
      this.writeToFile(this.errorFile, formattedMessage);
    }
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  error(message, meta = {}) {
    // Handle Error objects
    if (message instanceof Error) {
      const errorMeta = {
        ...meta,
        stack: message.stack,
        name: message.name
      };
      this.log('error', message.message, errorMeta);
    } else {
      this.log('error', message, meta);
    }
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  debug(message, meta = {}) {
    if (process.env.NODE_ENV === 'development') {
      this.log('debug', message, meta);
    }
  }

  // HTTP request logging
  logRequest(req, res, responseTime) {
    const message = `${req.method} ${req.originalUrl} - ${res.statusCode} - ${responseTime}ms`;
    const meta = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };

    if (res.statusCode >= 400) {
      this.error(message, meta);
    } else {
      this.info(message, meta);
    }
  }

  // Database query logging
  logQuery(query, duration) {
    if (process.env.NODE_ENV === 'development') {
      this.debug(`DB Query: ${query}`, { duration });
    }
  }

  // Security event logging
  logSecurityEvent(event, details = {}) {
    this.warn(`Security Event: ${event}`, details);
  }
}

module.exports = new Logger();
