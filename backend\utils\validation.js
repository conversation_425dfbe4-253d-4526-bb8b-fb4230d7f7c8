const { body, param, query, validationResult } = require('express-validator');

// Validation result handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Common validation rules
const commonValidations = {
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  password: body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  
  name: (field) => body(field)
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage(`${field} must be between 1 and 50 characters`),
  
  phone: body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  
  id: (field = 'id') => param(field)
    .isInt({ min: 1 })
    .withMessage(`${field} must be a positive integer`),
  
  price: (field) => body(field)
    .isFloat({ min: 0 })
    .withMessage(`${field} must be a positive number`),
  
  quantity: body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
  
  rating: body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  
  status: (allowedValues) => body('status')
    .isIn(allowedValues)
    .withMessage(`Status must be one of: ${allowedValues.join(', ')}`),
  
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]
};

// User validation rules
const userValidations = {
  register: [
    commonValidations.email,
    commonValidations.password,
    commonValidations.name('first_name'),
    commonValidations.name('last_name'),
    commonValidations.phone,
    handleValidationErrors
  ],
  
  login: [
    commonValidations.email,
    body('password').notEmpty().withMessage('Password is required'),
    handleValidationErrors
  ],
  
  updateProfile: [
    commonValidations.name('first_name').optional(),
    commonValidations.name('last_name').optional(),
    commonValidations.phone,
    body('preferences').optional().isObject().withMessage('Preferences must be an object'),
    handleValidationErrors
  ],
  
  changePassword: [
    body('current_password').notEmpty().withMessage('Current password is required'),
    commonValidations.password.withMessage('New password must be at least 6 characters long'),
    body('confirm_password')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Password confirmation does not match');
        }
        return true;
      }),
    handleValidationErrors
  ]
};

// Product validation rules
const productValidations = {
  create: [
    body('name').trim().isLength({ min: 1, max: 255 }).withMessage('Product name is required'),
    body('description').optional().isLength({ max: 5000 }).withMessage('Description too long'),
    body('sku').trim().isLength({ min: 1 }).withMessage('SKU is required'),
    commonValidations.price('price'),
    commonValidations.price('original_price').optional(),
    body('category_id').isInt({ min: 1 }).withMessage('Valid category is required'),
    body('stock_quantity').isInt({ min: 0 }).withMessage('Stock quantity must be non-negative'),
    body('brand').optional().trim().isLength({ max: 100 }),
    body('variants').optional().isObject(),
    body('tags').optional().isArray(),
    handleValidationErrors
  ],
  
  update: [
    body('name').optional().trim().isLength({ min: 1, max: 255 }),
    body('description').optional().isLength({ max: 5000 }),
    commonValidations.price('price').optional(),
    commonValidations.price('original_price').optional(),
    body('category_id').optional().isInt({ min: 1 }),
    body('stock_quantity').optional().isInt({ min: 0 }),
    body('status').optional().isIn(['active', 'inactive', 'draft', 'archived']),
    handleValidationErrors
  ]
};

// Order validation rules
const orderValidations = {
  create: [
    body('items').isArray({ min: 1 }).withMessage('Order must contain at least one item'),
    body('items.*.product_id').isInt({ min: 1 }).withMessage('Valid product ID required'),
    body('items.*.quantity').isInt({ min: 1 }).withMessage('Valid quantity required'),
    body('shipping_address_id').optional().isInt({ min: 1 }),
    body('billing_address_id').optional().isInt({ min: 1 }),
    body('payment_method').optional().trim().isLength({ min: 1 }),
    handleValidationErrors
  ],
  
  updateStatus: [
    commonValidations.status(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']),
    body('notes').optional().trim().isLength({ max: 1000 }),
    handleValidationErrors
  ]
};

// Cart validation rules
const cartValidations = {
  addItem: [
    body('product_id').isInt({ min: 1 }).withMessage('Valid product ID required'),
    commonValidations.quantity,
    body('options').optional().isObject(),
    handleValidationErrors
  ],
  
  updateItem: [
    commonValidations.quantity,
    handleValidationErrors
  ]
};

// Address validation rules
const addressValidations = {
  create: [
    commonValidations.name('first_name'),
    commonValidations.name('last_name'),
    body('address_line_1').trim().isLength({ min: 1 }).withMessage('Address line 1 is required'),
    body('city').trim().isLength({ min: 1 }).withMessage('City is required'),
    body('state').trim().isLength({ min: 1 }).withMessage('State is required'),
    body('postal_code').trim().isLength({ min: 1 }).withMessage('Postal code is required'),
    body('country').trim().isLength({ min: 2, max: 2 }).withMessage('Country code required'),
    body('type').optional().isIn(['shipping', 'billing', 'both']),
    handleValidationErrors
  ]
};

// Review validation rules
const reviewValidations = {
  create: [
    body('product_id').isInt({ min: 1 }).withMessage('Valid product ID required'),
    commonValidations.rating,
    body('title').optional().trim().isLength({ max: 200 }),
    body('comment').optional().trim().isLength({ max: 2000 }),
    handleValidationErrors
  ]
};

// Category validation rules
const categoryValidations = {
  create: [
    body('name').trim().isLength({ min: 1, max: 100 }).withMessage('Category name is required'),
    body('slug').trim().matches(/^[a-z0-9-]+$/).withMessage('Slug must contain only lowercase letters, numbers, and hyphens'),
    body('parent_id').optional().isInt({ min: 1 }),
    body('description').optional().isLength({ max: 1000 }),
    handleValidationErrors
  ]
};

module.exports = {
  handleValidationErrors,
  commonValidations,
  userValidations,
  productValidations,
  orderValidations,
  cartValidations,
  addressValidations,
  reviewValidations,
  categoryValidations
};
