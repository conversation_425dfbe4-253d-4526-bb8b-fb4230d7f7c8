// API Client for VAITH Backend Integration
class APIClient {
    constructor(baseURL) {
        this.baseURL = baseURL;
        this.token = localStorage.getItem('auth_token');
    }

    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('auth_token', token);
        } else {
            localStorage.removeItem('auth_token');
        }
    }

    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            // Handle different response types
            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = { success: response.ok, message: await response.text() };
            }

            if (!response.ok) {
                // Handle authentication errors
                if (response.status === 401) {
                    this.handleAuthError();
                }
                throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', {
                endpoint,
                error: error.message,
                options
            });
            
            // Show user-friendly error messages
            this.showErrorNotification(error.message);
            throw error;
        }
    }

    async get(endpoint, headers = {}) {
        return this.request(endpoint, { 
            method: 'GET', 
            headers: { ...this.getAuthHeaders(), ...headers }
        });
    }

    async post(endpoint, body = null, headers = {}) {
        return this.request(endpoint, {
            method: 'POST',
            headers: { ...this.getAuthHeaders(), ...headers },
            body: body ? JSON.stringify(body) : null
        });
    }

    async put(endpoint, body = null, headers = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            headers: { ...this.getAuthHeaders(), ...headers },
            body: body ? JSON.stringify(body) : null
        });
    }

    async delete(endpoint, headers = {}) {
        return this.request(endpoint, { 
            method: 'DELETE', 
            headers: { ...this.getAuthHeaders(), ...headers }
        });
    }

    handleAuthError() {
        console.warn('Authentication error - clearing token and redirecting to login');
        this.setToken(null);
        localStorage.removeItem('currentUser');
        
        // Don't redirect if already on login page
        if (!window.location.pathname.includes('login.html')) {
            window.location.href = 'login.html';
        }
    }

    showErrorNotification(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 1003;
            max-width: 300px;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    // Health check method
    async healthCheck() {
        try {
            const response = await fetch(`${this.baseURL.replace('/api', '')}/health`);
            return response.ok;
        } catch (error) {
            console.error('Backend health check failed:', error);
            return false;
        }
    }
}

// Initialize global API client
window.apiClient = new APIClient(window.CONFIG?.API_BASE_URL || 'http://localhost:3000/api');

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIClient;
}
