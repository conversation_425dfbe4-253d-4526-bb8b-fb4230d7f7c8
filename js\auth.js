// Authentication and Authorization System - Backend Integration

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.token = localStorage.getItem('auth_token');
        this.refreshToken = localStorage.getItem('refresh_token');
        this.onLoginCallbacks = [];
        this.onLogoutCallbacks = [];
        this.init();
    }

    async init() {
        // Check if user is logged in with valid token
        if (this.token) {
            try {
                await this.getCurrentUser();
            } catch (error) {
                console.warn('Invalid token, logging out:', error.message);
                this.logout(false); // Don't redirect during init
            }
        }
    }

    // Add callback for login events
    onLogin(callback) {
        this.onLoginCallbacks.push(callback);
    }

    // Add callback for logout events
    onLogout(callback) {
        this.onLogoutCallbacks.push(callback);
    }

    // Trigger login callbacks
    triggerLoginCallbacks(user) {
        this.onLoginCallbacks.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('Login callback error:', error);
            }
        });
    }

    // Trigger logout callbacks
    triggerLogoutCallbacks() {
        this.onLogoutCallbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Logout callback error:', error);
            }
        });
    }

    async login(email, password) {
        try {
            const response = await apiClient.post('/auth/login', {
                email,
                password
            });

            if (response.success) {
                const { user, token, refreshToken } = response.data;

                // Store tokens and user data
                this.token = token;
                this.refreshToken = refreshToken;
                this.currentUser = user;

                apiClient.setToken(token);
                localStorage.setItem('refresh_token', refreshToken);
                localStorage.setItem('currentUser', JSON.stringify(user));

                // Trigger login callbacks
                this.triggerLoginCallbacks(user);

                return user;
            } else {
                throw new Error(response.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    async register(userData) {
        try {
            const response = await apiClient.post('/auth/register', userData);

            if (response.success) {
                const { user, token, refreshToken } = response.data;

                // Store tokens and user data
                this.token = token;
                this.refreshToken = refreshToken;
                this.currentUser = user;

                apiClient.setToken(token);
                localStorage.setItem('refresh_token', refreshToken);
                localStorage.setItem('currentUser', JSON.stringify(user));

                // Trigger login callbacks
                this.triggerLoginCallbacks(user);

                return user;
            } else {
                throw new Error(response.message || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    }

    async getCurrentUser() {
        if (!this.token) {
            throw new Error('No authentication token');
        }

        try {
            const response = await apiClient.get('/auth/me');

            if (response.success) {
                this.currentUser = response.data.user;
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                return this.currentUser;
            } else {
                throw new Error(response.message || 'Failed to get current user');
            }
        } catch (error) {
            console.error('Get current user error:', error);
            throw error;
        }
    }

    logout(redirect = true) {
        // Clear all auth data
        this.token = null;
        this.refreshToken = null;
        this.currentUser = null;

        apiClient.setToken(null);
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('currentUser');

        // Trigger logout callbacks
        this.triggerLogoutCallbacks();

        // Redirect to login page
        if (redirect && !window.location.pathname.includes('login.html')) {
            window.location.href = 'login.html';
        }
    }

    isLoggedIn() {
        return this.token !== null && this.currentUser !== null;
    }

    isAdmin() {
        return this.currentUser && ['admin', 'super_admin'].includes(this.currentUser.role);
    }

    requireAuth() {
        if (!this.isLoggedIn()) {
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }

    requireAdmin() {
        if (!this.requireAuth()) return false;

        if (!this.isAdmin()) {
            alert('Access denied. Admin privileges required.');
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }

    getUser() {
        return this.currentUser;
    }

    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }

    async updateProfile(updates) {
        if (!this.isLoggedIn()) {
            throw new Error('User not logged in');
        }

        try {
            const response = await apiClient.put(`/users/${this.currentUser.id}`, updates);

            if (response.success) {
                this.currentUser = { ...this.currentUser, ...response.data.user };
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                return this.currentUser;
            } else {
                throw new Error(response.message || 'Failed to update profile');
            }
        } catch (error) {
            console.error('Update profile error:', error);
            throw error;
        }
    }

    async changePassword(currentPassword, newPassword) {
        if (!this.isLoggedIn()) {
            throw new Error('User not logged in');
        }

        try {
            const response = await apiClient.put(`/users/${this.currentUser.id}/password`, {
                current_password: currentPassword,
                new_password: newPassword
            });

            if (response.success) {
                return true;
            } else {
                throw new Error(response.message || 'Failed to change password');
            }
        } catch (error) {
            console.error('Change password error:', error);
            throw error;
        }
    }

    // Admin methods - these will be used by admin dashboard
    async getAllUsers(params = {}) {
        if (!this.isAdmin()) {
            throw new Error('Admin access required');
        }

        try {
            const queryString = new URLSearchParams(params).toString();
            const response = await apiClient.get(`/admin/users?${queryString}`);

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.message || 'Failed to get users');
            }
        } catch (error) {
            console.error('Get all users error:', error);
            throw error;
        }
    }

    async getUserStats() {
        if (!this.isAdmin()) {
            throw new Error('Admin access required');
        }

        try {
            const response = await apiClient.get('/admin/dashboard');

            if (response.success) {
                return response.data.stats;
            } else {
                throw new Error(response.message || 'Failed to get user stats');
            }
        } catch (error) {
            console.error('Get user stats error:', error);
            throw error;
        }
    }
}

// Initialize global auth manager
window.authManager = new AuthManager();

// Utility functions for UI
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getInitials(firstName, lastName) {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'active': 'status-active',
        'inactive': 'status-inactive',
        'suspended': 'status-suspended',
        'pending': 'status-pending'
    };
    return statusClasses[status] || 'status-inactive';
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthManager, formatDate, formatDateTime, getInitials, getStatusBadgeClass };
}
