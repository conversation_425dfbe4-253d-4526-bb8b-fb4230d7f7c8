// API Configuration
const config = {
    development: {
        API_BASE_URL: 'http://localhost:3001/api',
        FRONTEND_URL: 'http://localhost:8080'
    },
    production: {
        API_BASE_URL: 'https://api.vaith.com/api',
        FRONTEND_URL: 'https://vaith.com'
    }
};

// Determine current environment
const isDevelopment = window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.location.protocol === 'file:';

const currentConfig = config[isDevelopment ? 'development' : 'production'];

// Export configuration
window.CONFIG = currentConfig;

console.log('Environment:', isDevelopment ? 'development' : 'production');
console.log('API Base URL:', currentConfig.API_BASE_URL);
